#!/usr/bin/env python3
"""
setupbench评估结果成功率分析脚本

功能：分析指定目录下setupbench评估结果，统计成功率并输出格式化报告。

用法：python analyze_setupbench_results.py <folder_path>
示例：python analyze_setupbench_results.py /Users/<USER>/Projects/sweagentd/benchmarks/msbench/data-16683815878/
"""

import os
import sys
import json
import argparse
from pathlib import Path


def parse_eval_json(file_path):
    """
    解析eval.json文件，提取resolved字段的值
    
    Args:
        file_path (str): eval.json文件的路径
        
    Returns:
        bool or None: resolved字段的值，如果解析失败返回None
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            
        # eval.json的结构是 {"test-name": {"resolved": true/false}}
        # 获取第一个（也是唯一的）测试用例的resolved值
        for test_name, test_data in data.items():
            if isinstance(test_data, dict) and 'resolved' in test_data:
                return test_data['resolved']
                
        print(f"警告: {file_path} 中未找到resolved字段", file=sys.stderr)
        return None
        
    except json.JSONDecodeError as e:
        print(f"警告: 解析JSON文件失败 {file_path}: {e}", file=sys.stderr)
        return None
    except FileNotFoundError:
        print(f"警告: 文件不存在 {file_path}", file=sys.stderr)
        return None
    except Exception as e:
        print(f"警告: 读取文件时发生错误 {file_path}: {e}", file=sys.stderr)
        return None


def find_setupbench_directories(base_path):
    """
    查找所有setupbench评估结果目录
    
    Args:
        base_path (str): 基础目录路径
        
    Returns:
        list: 包含setupbench评估结果的目录列表
    """
    setupbench_dirs = []
    
    try:
        base_path = Path(base_path)
        if not base_path.exists():
            print(f"错误: 指定的目录不存在: {base_path}")
            return []
            
        if not base_path.is_dir():
            print(f"错误: 指定的路径不是目录: {base_path}")
            return []
            
        # 遍历所有子目录，查找符合命名模式的setupbench目录
        for item in base_path.iterdir():
            if item.is_dir() and item.name.startswith('setupbench.eval.'):
                eval_json_path = item / 'output' / 'eval.json'
                if eval_json_path.exists():
                    setupbench_dirs.append(str(eval_json_path))
                else:
                    print(f"警告: 在目录 {item} 中未找到 output/eval.json", file=sys.stderr)
                    
    except Exception as e:
        print(f"错误: 遍历目录时发生错误: {e}")
        return []
        
    return setupbench_dirs


def analyze_setupbench_results(folder_path):
    """
    分析setupbench评估结果
    
    Args:
        folder_path (str): 包含setupbench评估结果的文件夹路径
        
    Returns:
        tuple: (成功数量, 总数量, 成功率百分比)
    """
    eval_json_files = find_setupbench_directories(folder_path)
    
    if not eval_json_files:
        print("错误: 未找到任何有效的eval.json文件")
        return 0, 0, 0.0
        
    print(f"找到 {len(eval_json_files)} 个eval.json文件", file=sys.stderr)
    
    success_count = 0
    total_count = 0
    
    for eval_json_path in eval_json_files:
        resolved = parse_eval_json(eval_json_path)
        if resolved is not None:
            total_count += 1
            if resolved:
                success_count += 1
                
    if total_count == 0:
        print("错误: 没有成功解析任何eval.json文件")
        return 0, 0, 0.0
        
    success_rate = (success_count / total_count) * 100
    return success_count, total_count, success_rate


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='分析setupbench评估结果的成功率',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python analyze_setupbench_results.py /path/to/data-folder/
  python analyze_setupbench_results.py /Users/<USER>/Projects/sweagentd/benchmarks/msbench/data-16683815878/

输出格式:
  成功数量/总数量(百分比%)
  例如: 31/92(33.7%)
        """
    )
    
    parser.add_argument(
        'folder_path',
        help='包含setupbench评估结果的文件夹路径'
    )
    
    parser.add_argument(
        '-v', '--verbose',
        action='store_true',
        help='显示详细信息'
    )
    
    args = parser.parse_args()
    
    # 验证输入路径
    if not os.path.exists(args.folder_path):
        print(f"错误: 指定的目录不存在: {args.folder_path}")
        sys.exit(1)
        
    if not os.path.isdir(args.folder_path):
        print(f"错误: 指定的路径不是目录: {args.folder_path}")
        sys.exit(1)
    
    # 分析结果
    success_count, total_count, success_rate = analyze_setupbench_results(args.folder_path)
    
    if total_count == 0:
        sys.exit(1)
    
    # 输出结果
    print(f"{success_count}/{total_count}({success_rate:.1f}%)")
    
    if args.verbose:
        print(f"\n详细信息:", file=sys.stderr)
        print(f"  成功测试用例: {success_count}", file=sys.stderr)
        print(f"  总测试用例: {total_count}", file=sys.stderr)
        print(f"  成功率: {success_rate:.1f}%", file=sys.stderr)


if __name__ == '__main__':
    main()
