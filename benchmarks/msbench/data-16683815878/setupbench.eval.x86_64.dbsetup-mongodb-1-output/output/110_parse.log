+ . /opt/activate_python.sh
++ . /opt/miniconda3/etc/profile.d/conda.sh
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ '[' -z '' ']'
+++ export CONDA_SHLVL=0
+++ CONDA_SHLVL=0
+++ '[' -n '' ']'
+++++ dirname /opt/miniconda3/bin/conda
++++ dirname /opt/miniconda3/bin
+++ PATH=/opt/miniconda3/condabin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ export PATH
+++ '[' -z '' ']'
+++ PS1=
++ conda activate ces-env
++ local cmd=activate
++ case "$cmd" in
++ __conda_activate activate ces-env
++ '[' -n '' ']'
++ local ask_conda
+++ PS1=
+++ __conda_exe shell.posix activate ces-env
+++ '[' -n '' ']'
+++ /opt/miniconda3/bin/conda shell.posix activate ces-env
++ ask_conda='unset _CE_M
unset _CE_CONDA
PS1='\''(ces-env) '\''
export PATH='\''/opt/miniconda3/envs/ces-env/bin:/opt/miniconda3/condabin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/ces-env'\''
export CONDA_SHLVL='\''1'\''
export CONDA_DEFAULT_ENV='\''ces-env'\''
export CONDA_PROMPT_MODIFIER='\''(ces-env) '\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ eval 'unset _CE_M
unset _CE_CONDA
PS1='\''(ces-env) '\''
export PATH='\''/opt/miniconda3/envs/ces-env/bin:/opt/miniconda3/condabin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/ces-env'\''
export CONDA_SHLVL='\''1'\''
export CONDA_DEFAULT_ENV='\''ces-env'\''
export CONDA_PROMPT_MODIFIER='\''(ces-env) '\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+++ unset _CE_M
+++ unset _CE_CONDA
+++ PS1='(ces-env) '
+++ export PATH=/opt/miniconda3/envs/ces-env/bin:/opt/miniconda3/condabin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ PATH=/opt/miniconda3/envs/ces-env/bin:/opt/miniconda3/condabin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ export CONDA_PREFIX=/opt/miniconda3/envs/ces-env
+++ CONDA_PREFIX=/opt/miniconda3/envs/ces-env
+++ export CONDA_SHLVL=1
+++ CONDA_SHLVL=1
+++ export CONDA_DEFAULT_ENV=ces-env
+++ CONDA_DEFAULT_ENV=ces-env
+++ export 'CONDA_PROMPT_MODIFIER=(ces-env) '
+++ CONDA_PROMPT_MODIFIER='(ces-env) '
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ __conda_hashr
++ '[' -n '' ']'
++ '[' -n '' ']'
++ hash -r
+ python /parse.py /testbed/_output_/test_results.json
