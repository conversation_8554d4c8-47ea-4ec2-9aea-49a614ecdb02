diff --git a/nohup.out b/nohup.out
new file mode 100644
index 0000000..e04e03e
--- /dev/null
+++ b/nohup.out
@@ -0,0 +1,11 @@
+about to fork child process, waiting until server is ready for connections.
+forked process: 13848
+child process started successfully, parent exiting
+about to fork child process, waiting until server is ready for connections.
+forked process: 14825
+ERROR: child process failed, exited with 48
+To see additional information in this output, start without the "--fork" option.
+about to fork child process, waiting until server is ready for connections.
+forked process: 15642
+ERROR: child process failed, exited with 48
+To see additional information in this output, start without the "--fork" option.
diff --git a/setup_mongodb.sh b/setup_mongodb.sh
new file mode 100755
index 0000000..572c885
--- /dev/null
+++ b/setup_mongodb.sh
@@ -0,0 +1,27 @@
+#!/bin/bash
+set -e
+
+# Update package lists and install prerequisites
+DEBIAN_FRONTEND=noninteractive apt-get update -y
+apt-get install -y gnupg wget software-properties-common
+
+# Add MongoDB 6.0 repository
+wget -qO - https://www.mongodb.org/static/pgp/server-6.0.asc | apt-key add -
+echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu jammy/mongodb-org/6.0 multiverse" | tee /etc/apt/sources.list.d/mongodb-org-6.0.list
+apt-get update -y
+
+# Install MongoDB packages
+DEBIAN_FRONTEND=noninteractive apt-get install -y mongodb-org
+
+# Ensure data directory exists
+mkdir -p /data/db
+
+# Start MongoDB in background
+nohup mongod --fork --logpath /var/log/mongod.log --bind_ip 127.0.0.1 --dbpath /data/db
+
+# Import users data
+mongoimport --db benchmark_db --collection users --file "$(pwd)/users.json" --jsonArray
+
+# Print success with count
+COUNT=$(mongosh benchmark_db --quiet --eval 'db.users.countDocuments()')
+echo "Import successful. Document count: $COUNT"
