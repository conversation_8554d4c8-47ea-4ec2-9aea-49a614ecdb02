diff --git a/__pycache__/tasks.cpython-310.pyc b/__pycache__/tasks.cpython-310.pyc
new file mode 100644
index 0000000..4ac8e23
Binary files /dev/null and b/__pycache__/tasks.cpython-310.pyc differ
diff --git a/celerybeat-schedule.db b/celerybeat-schedule.db
new file mode 100644
index 0000000..4c244dc
Binary files /dev/null and b/celerybeat-schedule.db differ
diff --git a/test_tasks.py b/test_tasks.py
new file mode 100644
index 0000000..fec5f73
--- /dev/null
+++ b/test_tasks.py
@@ -0,0 +1,19 @@
+import time
+import redis
+import unittest
+
+class TestBeatTask(unittest.TestCase):
+    def test_beat_sets_key(self):
+        r = redis.Redis(host='localhost', port=6379, db=0)
+        # Wait for up to 30 seconds to allow the beat scheduled task to run
+        timeout = 30
+        start = time.time()
+        while time.time() - start < timeout:
+            value = r.get('beat_check')
+            if value == b'alive':
+                break
+            time.sleep(1)
+        self.assertEqual(value, b'alive', "Celery beat did not set the key 'beat_check' to 'alive'.")
+
+if __name__ == '__main__':
+    unittest.main()
