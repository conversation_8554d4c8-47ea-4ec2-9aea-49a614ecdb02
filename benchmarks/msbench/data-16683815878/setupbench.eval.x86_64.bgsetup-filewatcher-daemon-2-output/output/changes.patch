diff --git a/README.md b/README.md
new file mode 100644
index 0000000..fe313d3
--- /dev/null
+++ b/README.md
@@ -0,0 +1,36 @@
+# Watcher Utility
+
+`watcher.py` recursively monitors a directory (`$WATCH_PATH`, default `/watched`) and logs create, modify, and delete events as JSO<PERSON> lines to `/var/log/watcher.json.log`.
+
+## Usage
+
+```bash
+# Ensure necessary packages are installed
+apt-get update && apt-get install -y python3 python3-pip
+pip3 install watchdog
+
+# Make sure the watch directory exists
+mkdir -p /watched
+
+# Run the watcher (background)
+export WATCH_PATH=/watched
+python3 /testbed/watcher.py &
+```
+
+## Supervision
+
+In a production setup, create a systemd service file:
+
+```
+[Unit]
+Description=Directory Watcher Service
+
+[Service]
+ExecStart=/usr/bin/python3 /testbed/watcher.py
+Restart=always
+
+[Install]
+WantedBy=multi-user.target
+```
+
+Copy it to `/etc/systemd/system/watcher.service` then `systemctl enable --now watcher`.
diff --git a/test_watcher.sh b/test_watcher.sh
new file mode 100755
index 0000000..37a1479
--- /dev/null
+++ b/test_watcher.sh
@@ -0,0 +1,36 @@
+#!/bin/bash
+
+# Ensure environment path variables
+export WATCH_PATH=/tmp/watched_test
+LOG=/var/log/watcher.json.log
+
+mkdir -p "$WATCH_PATH"
+
+# Remove previous log entries for test path (avoid interfering). We'll copy to a temp file to later diff.
+cp /dev/null "$LOG"
+
+# Start watcher in background
+python3 /testbed/watcher.py &
+W_PID=$!
+
+# Allow watcher to start
+sleep 1
+
+# Perform actions: create, modify, delete a file
+FILE="$WATCH_PATH/testfile.txt"
+
+echo "hello" > "$FILE"
+sleep 0.1
+echo "world" >> "$FILE"
+sleep 0.1
+rm "$FILE"
+
+# Give watcher time to log
+sleep 1
+
+# Kill watcher
+kill $W_PID
+wait $W_PID 2>/dev/null
+
+# Extract events for testfile
+grep "$FILE" "$LOG" || true
