diff --git a/README.md b/README.md
new file mode 100644
index 0000000..4c783af
--- /dev/null
+++ b/README.md
@@ -0,0 +1,15 @@
+# Watcher Service
+
+This service monitors the `/watched` directory for new files and logs creation events to `/var/log/watcher.log`.
+
+## Setup
+
+Supervisor config is included in `/etc/supervisor/conf.d/watcher.conf` and automatically starts the watcher script.
+
+## Testing
+
+Run the provided script to verify it works:
+
+```bash
+/testbed/test_watcher.sh
+```
diff --git a/test_watcher.sh b/test_watcher.sh
new file mode 100755
index 0000000..6bb2876
--- /dev/null
+++ b/test_watcher.sh
@@ -0,0 +1,12 @@
+#!/bin/bash
+echo "Setting up test file."
+FNAME=/watched/$(date +%s).txt
+echo "test" > "$FNAME"
+sleep 1
+if grep -q "File created: $FNAME" /var/log/watcher.log; then
+    echo "Watcher test passed"
+    exit 0
+else
+    echo "Watcher test failed"
+    exit 1
+fi
diff --git a/watcher.py b/watcher.py
index c5eedfb..74f136b 100644
--- a/watcher.py
+++ b/watcher.py
@@ -6,7 +6,7 @@ from watchdog.events import FileSystemEventHandler
 class WatchHandler(FileSystemEventHandler):
     def on_created(self, event):
         if not event.is_directory:
-            print(f"File created: {event.src_path}")
+            print(f"File created: {event.src_path}", flush=True)
 
 if __name__ == "__main__":
     import os
