diff --git a/test_mongo.py b/test_mongo.py
new file mode 100644
index 0000000..9bac789
--- /dev/null
+++ b/test_mongo.py
@@ -0,0 +1,6 @@
+from pymongo import MongoClient
+
+uri = "********************************************************************"
+client = MongoClient(uri)
+print('Connected to database:', client.breaker if False else 'benchmark_db')
+print('Collections:', client['benchmark_db'].list_collection_names())
