Evaluating success with the following command: verify_web() { make serve & pid=$!; sleep 90; code=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000); [ $code -eq 200 ] && echo "Setup successful" || echo "Setup failed"; pgid=$(ps -o pgid= $pid | tr -d ' '); kill -TERM -$pgid 2>/dev/null; }; verify_web || echo "Setup failed"
Task type: reposetup
Using new terminal session: True
Running test command...
Test command output: /testbed/tools/build.sh js
Building javascript...
Setup failed
Setup failed
/usr/bin/env: ‘node’: No such file or directory
make: *** [Makefile:78: js/lib/beautifier.js] Error 1

Setup success: {'success': False}
