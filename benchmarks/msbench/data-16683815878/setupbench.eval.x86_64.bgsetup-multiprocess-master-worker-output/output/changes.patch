diff --git a/verify.sh b/verify.sh
new file mode 100755
index 0000000..add170e
--- /dev/null
+++ b/verify.sh
@@ -0,0 +1,14 @@
+#!/bin/bash
+SUPERVISORCTL=/usr/bin/supervisorctl
+if [ ! -x "$SUPERVISORCTL" ]; then
+  echo "supervisorctl not installed" 1>&2
+  exit 1
+fi
+RUNNING=$($SUPERVISORCTL status | grep -c RUNNING)
+if [ "$RUNNING" -ge 2 ]; then
+  echo SUCCESS: $RUNNING processes running
+  exit 0
+else
+  echo FAILURE: Expected 2 processes, got $RUNNING
+  exit 1
+fi
