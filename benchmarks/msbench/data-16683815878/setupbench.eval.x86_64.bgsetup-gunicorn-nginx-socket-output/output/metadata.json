{"instance_id": "bgsetup-gunicorn-nginx-socket", "problem_statement": "You are in a benchmark that evaluates your ability to interpret documentation, install dependencies, configure, and setup projects in a completely minimal environment.\n\nEnvironment Details\nYou are operating inside a fresh Ubuntu 22.04 environment with no tools or packages preinstalled — not even python3 or build-essential.\n\nKey constraints:\n\nYou must install all required dependencies globally using apt or other system-level installers.\nDo not create or use virtual environments (e.g., no venv, conda, or containerization).\nYour setup should be non-interactive and able to run in a headless CI environment.\nAssume you are starting from scratch — no assumptions about preinstalled tools.\nYou have root privileges in the environment, so there is no need to use sudo.\n\nTask:\nInstall Gunicorn, Flask, and NGINX. Configure Gunicorn to serve a Flask app via a Unix domain socket at /tmp/gunicorn.sock. Then configure NGINX to reverse proxy requests to that socket and serve the app on http://localhost/. The Flask app is located in /testbed/app.py. Ensure both services run persistently using supervisord.\n\nAdditional Instructions:\n- At the end of the setup, you will be evaluated by running a single-line success command to confirm the task was successful.\n\n"}