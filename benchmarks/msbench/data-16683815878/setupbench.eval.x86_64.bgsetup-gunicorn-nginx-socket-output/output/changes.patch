diff --git a/__pycache__/app.cpython-310.pyc b/__pycache__/app.cpython-310.pyc
new file mode 100644
index 0000000..cd38b2f
Binary files /dev/null and b/__pycache__/app.cpython-310.pyc differ
diff --git a/__pycache__/gunicorn_config.cpython-310.pyc b/__pycache__/gunicorn_config.cpython-310.pyc
new file mode 100644
index 0000000..bf360c6
Binary files /dev/null and b/__pycache__/gunicorn_config.cpython-310.pyc differ
diff --git a/gunicorn_config.py b/gunicorn_config.py
new file mode 100644
index 0000000..f67a193
--- /dev/null
+++ b/gunicorn_config.py
@@ -0,0 +1,2 @@
+bind = 'unix:/tmp/gunicorn.sock'
+workers = 2
diff --git a/supervisord.conf b/supervisord.conf
new file mode 100644
index 0000000..4e3a7f5
--- /dev/null
+++ b/supervisord.conf
@@ -0,0 +1,16 @@
+[supervisord]
+nodaemon=true
+
+[program:gunicorn]
+command=gunicorn -c /testbed/gunicorn_config.py app:app
+stdout_logfile=/var/log/gunicorn.stdout.log
+stderr_logfile=/var/log/gunicorn.stderr.log
+autostart=true
+autoreset=true
+
+[program:nginx]
+command=/usr/sbin/nginx -g "daemon off;"
+stdout_logfile=/var/log/nginx.stdout.log
+stderr_logfile=/var/log/nginx.stderr.log
+autostart=true
+autoreset=true
diff --git a/supervisord.log b/supervisord.log
new file mode 100644
index 0000000..b43740c
--- /dev/null
+++ b/supervisord.log
@@ -0,0 +1,6 @@
+2025-08-01 20:12:53,074 CRIT Supervisor is running as root.  Privileges were not dropped because no user is specified in the config file.  If you intend to run as root, you can set user=root in the config file to avoid this message.
+2025-08-01 20:12:53,076 INFO supervisord started with pid 12522
+2025-08-01 20:12:54,078 INFO spawned: 'gunicorn' with pid 12523
+2025-08-01 20:12:54,080 INFO spawned: 'nginx' with pid 12524
+2025-08-01 20:12:55,262 INFO success: gunicorn entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
+2025-08-01 20:12:55,262 INFO success: nginx entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
