diff --git a/README.md b/README.md
new file mode 100644
index 0000000..b9d036b
--- /dev/null
+++ b/README.md
@@ -0,0 +1,49 @@
+# Environment Setup for msbench
+
+This repository contains SQL migration scripts used by the benchmark environment.
+
+## Environment Setup Steps
+
+1. **Install MySQL Server**
+   * We install `mysql-server` via `apt`.
+   * MySQL service is configured to listen on **port 3307** to avoid conflicts with any existing MySQL installation.
+
+2. **Configure MySQL**
+   * A configuration file is placed in `/etc/mysql/conf.d/bench_port.cnf` specifying `port = 3307`.
+   * Service restarted after configuration change.
+
+3. **Create Database & User**
+   * Database `benchmark_db` is created.
+   * User `benchmark_user` with password `benchmark_pass` is created and granted full privileges on the database.
+
+4. **Apply Migrations & Seed Data**
+   * The SQL migration files in the `migrations/` directory are gzipped.
+   * They are decompressed and piped into the `mysql` command for user `benchmark_user` on port `3307`.
+
+5. **Validation**
+   * A simple `SELECT` query verifies that the `products` table contains seeded data.
+
+## Files in This Repository
+
+* `migrations/01_seed.sql.gz` – Seed data for categories & products.
+* `migrations/02_schema.sql.gz` – Schema definition for categories, products and a view.
+* `README.md` – This documentation.
+
+## Commands Used (for reference)
+
+```bash
+apt-get update -y
+DEBIAN_FRONTEND=noninteractive apt-get install -y mysql-server gzip
+echo -e '[mysqld]\nport=3307' > /etc/mysql/conf.d/bench_port.cnf
+service mysql restart
+
+mysql -u root -e "CREATE DATABASE benchmark_db; CREATE USER 'benchmark_user'@'localhost' IDENTIFIED BY 'benchmark_pass'; GRANT ALL ON benchmark_db.* TO 'benchmark_user'@'localhost'; FLUSH PRIVILEGES;"
+
+gunzip -c migrations/02_schema.sql.gz | sed -e "s/DEFINER=[^ ]* //" | mysql -u benchmark_user -pbenchmark_pass -P 3307
+
+gunzip -c migrations/01_seed.sql.gz | mysql -u benchmark_user -pbenchmark_pass -P 3307 benchmark_db
+
+mysql -u benchmark_user -pbenchmark_pass -P 3307 -D benchmark_db -e "SELECT * FROM products;"
+```
+
+This setup is fully non‑interactive and suitable for CI environments.
