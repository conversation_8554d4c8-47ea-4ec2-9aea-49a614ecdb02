Evaluating success with the following command: bin/cassandra -R && sleep 80 && bin/nodetool status | grep -q 'UN\s\+127.0.0.1' && echo "Setup successful" || echo "Setup failed"
Task type: reposetup
Using new terminal session: True
Running test command...
Test command output: CompileCommand: dontinline org/apache/cassandra/db/Columns$Serializer.deserializeLargeSubset(Lorg/apache/cassandra/io/util/DataInputPlus;Lorg/apache/cassandra/db/Columns;I)Lorg/apache/cassandra/db/Columns;
CompileCommand: dontinline org/apache/cassandra/db/Columns$Serializer.serializeLargeSubset(Ljava/util/Collection;ILorg/apache/cassandra/db/Columns;ILorg/apache/cassandra/io/util/DataOutputPlus;)V
CompileCommand: dontinline org/apache/cassandra/db/Columns$Serializer.serializeLargeSubsetSize(Ljava/util/Collection;ILorg/apache/cassandra/db/Columns;I)I
CompileCommand: dontinline org/apache/cassandra/db/commitlog/AbstractCommitLogSegmentManager.advanceAllocatingFrom(Lorg/apache/cassandra/db/commitlog/CommitLogSegment;)V
CompileCommand: dontinline org/apache/cassandra/db/transform/BaseIterator.tryGetMoreContents()Z
CompileCommand: dontinline org/apache/cassandra/db/transform/StoppingTransformation.stop()V
CompileCommand: dontinline org/apache/cassandra/db/transform/StoppingTransformation.stopInPartition()V
CompileCommand: dontinline org/apache/cassandra/io/util/BufferedDataOutputStreamPlus.doFlush(I)V
CompileCommand: dontinline org/apache/cassandra/io/util/BufferedDataOutputStreamPlus.writeSlow(JI)V
CompileCommand: dontinline org/apache/cassandra/io/util/RebufferingInputStream.readPrimitiveSlowly(I)J
CompileCommand: dontinline org/apache/cassandra/service/accord/serializers/IVersionedWithKeysSerializer$AbstractWithKeysSerializer.serializeLargeSubset(Laccord/primitives/Routables;ILaccord/primitives/Routables;ILorg/apache/cassandra/io/util/DataOutputPlus;)V
CompileCommand: dontinline org/apache/cassandra/service/accord/serializers/IVersionedWithKeysSerializer$AbstractWithKeysSerializer.serializeLargeSubsetSize(Laccord/primitives/Routables;ILaccord/primitives/Routables;I)J
CompileCommand: dontinline org/apache/cassandra/utils/ByteBufferUtil.readLeastSignificantBytesSlow(ILjava/nio/ByteBuffer;)J
CompileCommand: dontinline org/apache/cassandra/utils/ByteBufferUtil.writeMostSignificantBytesSlow(JILjava/nio/ByteBuffer;)V
CompileCommand: dontinline org/apache/cassandra/utils/vint/VIntCoding.readUnsignedVIntSlow(Ljava/nio/ByteBuffer;B)J
CompileCommand: dontinline org/apache/cassandra/utils/vint/VIntCoding.readUnsignedVIntSlow(Ljava/nio/ByteBuffer;IB)J
CompileCommand: exclude org/apache/cassandra/utils/JVMStabilityInspector.forceHeapSpaceOomMaybe(Ljava/lang/OutOfMemoryError;)V
CompileCommand: inline org/apache/cassandra/db/NativeDecoratedKey.address()J
CompileCommand: inline org/apache/cassandra/db/NativeDecoratedKey.length()I
CompileCommand: inline org/apache/cassandra/db/rows/UnfilteredSerializer.serializeRowBody(Lorg/apache/cassandra/db/rows/Row;ILorg/apache/cassandra/db/rows/SerializationHelper;Lorg/apache/cassandra/io/util/DataOutputPlus;)V
CompileCommand: inline org/apache/cassandra/io/util/Memory.checkBounds(JJ)V
CompileCommand: inline org/apache/cassandra/io/util/SafeMemory.checkBounds(JJ)V
CompileCommand: inline org/apache/cassandra/io/util/TrackedDataInputPlus.checkCanRead(I)V
CompileCommand: inline org/apache/cassandra/net/FrameDecoderWith8bHeader.decode(Ljava/util/Collection;Lorg/apache/cassandra/net/ShareableBytes;I)V
CompileCommand: inline org/apache/cassandra/service/accord/serializers/IVersionedWithKeysSerializer$AbstractWithKeysSerializer.deserializeLargeSubset(Lorg/apache/cassandra/io/util/DataInputPlus;Laccord/primitives/Routables;IILjava/util/function/IntFunction;)[Laccord/primitives/Routable;
CompileCommand: inline org/apache/cassandra/service/reads/repair/RowIteratorMergeListener.applyToPartition(ILjava/util/function/Consumer;)V
CompileCommand: inline org/apache/cassandra/utils/AsymmetricOrdering.selectBoundary(Lorg/apache/cassandra/utils/AsymmetricOrdering/Op;II)I
CompileCommand: inline org/apache/cassandra/utils/AsymmetricOrdering.strictnessOfLessThan(Lorg/apache/cassandra/utils/AsymmetricOrdering/Op;)I
CompileCommand: inline org/apache/cassandra/utils/BloomFilter.indexes(Lorg/apache/cassandra/utils/IFilter/FilterKey;)[J
CompileCommand: inline org/apache/cassandra/utils/BloomFilter.setIndexes(JJIJ[J)V
CompileCommand: inline org/apache/cassandra/utils/ByteBufferUtil.compare(Ljava/nio/ByteBuffer;[B)I
CompileCommand: inline org/apache/cassandra/utils/ByteBufferUtil.compare([BLjava/nio/ByteBuffer;)I
CompileCommand: inline org/apache/cassandra/utils/ByteBufferUtil.compareUnsigned(Ljava/nio/ByteBuffer;Ljava/nio/ByteBuffer;)I
CompileCommand: inline org/apache/cassandra/utils/FastByteOperations$UnsafeOperations.compareTo(Ljava/lang/Object;JILjava/lang/Object;JI)I
CompileCommand: inline org/apache/cassandra/utils/FastByteOperations$UnsafeOperations.compareTo(Ljava/lang/Object;JILjava/nio/ByteBuffer;)I
CompileCommand: inline org/apache/cassandra/utils/FastByteOperations$UnsafeOperations.compareTo(Ljava/nio/ByteBuffer;Ljava/nio/ByteBuffer;)I
CompileCommand: inline org/apache/cassandra/utils/concurrent/Awaitable$AsyncAwaitable.await(Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;Ljava/util/function/Predicate;Lorg/apache/cassandra/utils/concurrent/Awaitable;)Lorg/apache/cassandra/utils/concurrent/Awaitable;
CompileCommand: inline org/apache/cassandra/utils/concurrent/Awaitable$AsyncAwaitable.awaitUntil(Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;Ljava/util/function/Predicate;Lorg/apache/cassandra/utils/concurrent/Awaitable;J)Z
CompileCommand: inline org/apache/cassandra/utils/concurrent/Awaitable$AsyncAwaitable.register(Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;Ljava/util/function/Predicate;Lorg/apache/cassandra/utils/concurrent/Awaitable;)Lorg/apache/cassandra/utils/concurrent/WaitQueue/Signal;
CompileCommand: inline org/apache/cassandra/utils/concurrent/Awaitable$AsyncAwaitable.signalAll(Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;Lorg/apache/cassandra/utils/concurrent/Awaitable;)V
CompileCommand: inline org/apache/cassandra/utils/concurrent/IntrusiveStack.getAndPush(Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;Ljava/lang/Object;Lorg/apache/cassandra/utils/concurrent/IntrusiveStack;)Lorg/apache/cassandra/utils/concurrent/IntrusiveStack;
CompileCommand: inline org/apache/cassandra/utils/concurrent/IntrusiveStack.getAndPush(Ljava/util/function/Function;Lorg/apache/cassandra/utils/concurrent/IntrusiveStack/Setter;Ljava/lang/Object;Lorg/apache/cassandra/utils/concurrent/IntrusiveStack;)Lorg/apache/cassandra/utils/concurrent/IntrusiveStack;
CompileCommand: inline org/apache/cassandra/utils/concurrent/ListenerList.push(Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;Ljava/lang/Object;Lorg/apache/cassandra/utils/concurrent/ListenerList;)V
CompileCommand: inline org/apache/cassandra/utils/concurrent/ListenerList.pushExclusive(Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;Ljava/lang/Object;Lorg/apache/cassandra/utils/concurrent/ListenerList;)V
CompileCommand: inline org/apache/cassandra/utils/memory/BufferPool$LocalPool.tryGetInternal(IZ)Ljava/nio/ByteBuffer;
CompileCommand: inline org/apache/cassandra/utils/vint/VIntCoding.encodeUnsignedVInt(JI)[B
CompileCommand: inline org/apache/cassandra/utils/vint/VIntCoding.writeUnsignedVInt(JLjava/lang/Object;ILorg/apache/cassandra/db/marshal/ValueAccessor;)I
CompileCommand: inline org/apache/cassandra/utils/vint/VIntCoding.writeUnsignedVInt(JLjava/nio/ByteBuffer;)V
CompileCommand: inline org/apache/cassandra/utils/vint/VIntCoding.writeUnsignedVInt(JLorg/apache/cassandra/io/util/DataOutputPlus;)V
CompileCommand: inline org/apache/cassandra/utils/vint/VIntCoding.writeUnsignedVInt32(ILjava/lang/Object;ILorg/apache/cassandra/db/marshal/ValueAccessor;)I
CompileCommand: inline org/apache/cassandra/utils/vint/VIntCoding.writeUnsignedVInt32(ILjava/nio/ByteBuffer;)V
CompileCommand: inline org/apache/cassandra/utils/vint/VIntCoding.writeVInt(JLjava/lang/Object;ILorg/apache/cassandra/db/marshal/ValueAccessor;)I
CompileCommand: inline org/apache/cassandra/utils/vint/VIntCoding.writeVInt(JLjava/nio/ByteBuffer;)V
CompileCommand: inline org/apache/cassandra/utils/vint/VIntCoding.writeVInt(JLorg/apache/cassandra/io/util/DataOutputPlus;)V
CompileCommand: inline org/apache/cassandra/utils/vint/VIntCoding.writeVInt32(ILjava/lang/Object;ILorg/apache/cassandra/db/marshal/ValueAccessor;)I
CompileCommand: inline org/apache/cassandra/utils/vint/VIntCoding.writeVInt32(ILjava/nio/ByteBuffer;)V
CompileCommand: inline org/apache/cassandra/utils/vint/VIntCoding.writeVInt32(ILorg/apache/cassandra/io/util/DataOutputPlus;)V
INFO  [main] 2025-08-01T20:09:11,090 YamlConfigurationLoader.java:106 - Configuration location: file:/testbed/conf/cassandra.yaml
INFO  [main] 2025-08-01T20:09:11,532 Config.java:1442 - Node configuration:[accord_preaccept_timeout=1s; addresses_config=null; allocate_tokens_for_keyspace=null; allocate_tokens_for_local_replication_factor=3; allow_extra_insecure_udfs=false; allow_filtering_enabled=true; allow_insecure_udfs=false; alter_table_enabled=true; audit_logging_options=AuditLogOptions{enabled=false, logger='BinAuditLoggernull', included_keyspaces='', excluded_keyspaces='system,system_schema,system_virtual_schema', included_categories='', excluded_categories='', included_users='', excluded_users='', audit_logs_dir='logs/audit', archive_command='', roll_cycle='HOURLY', block=true, max_queue_weight=268435456, max_log_size=17179869184, max_archive_retries=10}; auth_cache_warming_enabled=false; auth_read_consistency_level=LOCAL_QUORUM; auth_write_consistency_level=EACH_QUORUM; authenticator=AllowAllAuthenticatornull; authorizer=AllowAllAuthorizernull; auto_bootstrap=true; auto_hints_cleanup_enabled=false; auto_optimise_full_repair_streams=false; auto_optimise_inc_repair_streams=false; auto_optimise_preview_repair_streams=false; auto_repair=AutoRepairConfig{enabled=false, repair_check_interval=5m, history_clear_delete_hosts_buffer_interval=2h, repair_task_min_duration=5s, global_settings=Options{enabled=false, repair_by_keyspace=true, number_of_repair_threads=1, parallel_repair_count=3, parallel_repair_percentage=3, allow_parallel_replica_repair=false, allow_parallel_replica_repair_across_schedules=true, sstable_upper_threshold=50000, min_repair_interval=24h, ignore_dcs=[], repair_primary_token_range_only=true, force_repair_new_node=false, table_max_repair_time=6h, materialized_view_repair_enabled=false, token_range_splitter=org.apache.cassandra.repair.autorepair.RepairTokenRangeSplitter{}, intial_scheduler_delay=5m, repair_session_timeout=3h}, repair_type_overrides={}}; auto_snapshot=true; auto_snapshot_ttl=null; autocompaction_on_startup_enabled=true; automatic_sstable_upgrade=false; available_processors=null; back_pressure_enabled=false; back_pressure_strategy=null; batch_size_fail_threshold=50KiB; batch_size_warn_threshold=5KiB; batchlog_endpoint_strategy=random_remote; batchlog_replay_throttle=1024KiB; block_for_peers_in_remote_dcs=false; block_for_peers_timeout_in_secs=10; broadcast_address=null; broadcast_rpc_address=null; buffer_pool_use_heap_if_exhausted=false; bulk_load_enabled=true; cache_load_timeout=30s; cas_contention_timeout=5000ms; cdc_block_writes=true; cdc_enabled=false; cdc_free_space_check_interval=250ms; cdc_on_repair_enabled=true; cdc_raw_directory=null; cdc_total_space=0MiB; check_for_duplicate_rows_during_compaction=true; check_for_duplicate_rows_during_reads=true; cidr_authorizer=AllowAllCIDRAuthorizernull; client_encryption_options=<REDACTED>; client_error_reporting_exclusions=SubnetGroups{subnets=[]}; client_request_size_metrics_enabled=true; cluster_name=Test Cluster; cms_await_timeout=120000ms; cms_default_max_retries=10; cms_default_max_retry_backoff=null; cms_default_retry_backoff=null; cms_retry_delay=50ms*attempts <= 500ms ... 100ms*attempts <= 1s,retries=10; collection_list_size_fail_threshold=null; collection_list_size_warn_threshold=null; collection_map_size_fail_threshold=null; collection_map_size_warn_threshold=null; collection_set_size_fail_threshold=null; collection_set_size_warn_threshold=null; collection_size_fail_threshold=null; collection_size_warn_threshold=null; column_ascii_value_size_fail_threshold=null; column_ascii_value_size_warn_threshold=null; column_blob_value_size_fail_threshold=null; column_blob_value_size_warn_threshold=null; column_index_cache_size=2KiB; column_index_size=null; column_text_and_varchar_value_size_fail_threshold=null; column_text_and_varchar_value_size_warn_threshold=null; column_value_size_fail_threshold=null; column_value_size_warn_threshold=null; column_varchar_value_size_fail_threshold=null; column_varchar_value_size_warn_threshold=null; columns_per_table_fail_threshold=-1; columns_per_table_warn_threshold=-1; commit_failure_policy=stop; commitlog_compression=null; commitlog_directory=null; commitlog_disk_access_mode=legacy; commitlog_max_compression_buffers_in_pool=3; commitlog_segment_size=32MiB; commitlog_sync=periodic; commitlog_sync_group_window=0ms; commitlog_sync_period=10000ms; commitlog_total_space=null; compact_tables_enabled=true; compaction_throughput=64MiB/s; compressed_read_ahead_buffer_size=256KiB; concurrent_accord_operations=32; concurrent_compactors=null; concurrent_counter_writes=32; concurrent_index_builders=2; concurrent_materialized_view_builders=1; concurrent_materialized_view_writes=32; concurrent_merkle_tree_requests=0; concurrent_reads=32; concurrent_validations=0; concurrent_writes=32; consecutive_message_errors_threshold=1; consensus_migration_cache_size=null; coordinator_read_size_fail_threshold=null; coordinator_read_size_warn_threshold=null; corrupted_tombstone_strategy=disabled; counter_cache_keys_to_save=**********; counter_cache_save_period=7200s; counter_cache_size=null; counter_write_request_timeout=1000ms; cql_start_time=REQUEST; credentials_cache_active_update=false; credentials_cache_max_entries=1000; credentials_update_interval=null; credentials_validity=2000ms; crypto_provider=org.apache.cassandra.security.DefaultCryptoProvider{fail_on_missing_provider=false}; data_disk_usage_max_disk_size=null; data_disk_usage_percentage_fail_threshold=-1; data_disk_usage_percentage_warn_threshold=-1; data_file_directories=[Ljava.lang.String;@40238dd0; default_compaction=null; default_keyspace_rf=1; default_secondary_index=legacy_local_table; default_secondary_index_enabled=true; denylist_consistency_level=QUORUM; denylist_initial_load_retry=5s; denylist_max_keys_per_table=1000; denylist_max_keys_total=10000; denylist_range_reads_enabled=true; denylist_reads_enabled=true; denylist_refresh=600s; denylist_writes_enabled=true; diagnostic_events_enabled=false; discovery_timeout=30s; disk_access_mode=mmap_index_only; disk_failure_policy=stop; disk_optimization_estimate_percentile=0.95; disk_optimization_page_cross_chance=0.1; disk_optimization_strategy=ssd; drop_compact_storage_enabled=false; drop_keyspace_enabled=true; drop_truncate_table_enabled=true; dump_heap_on_uncaught_exception=false; dynamic_data_masking_enabled=false; dynamic_snitch=true; dynamic_snitch_badness_threshold=1.0; dynamic_snitch_reset_interval=600000ms; dynamic_snitch_update_interval=100ms; endpoint_snitch=SimpleSnitch; enforce_native_deadline_for_hints=false; entire_sstable_inter_dc_stream_throughput_outbound=24MiB/s; entire_sstable_stream_throughput_outbound=24MiB/s; epoch_aware_debounce_inflight_tracker_max_size=100; failure_detector=FailureDetector; fields_per_udt_fail_threshold=-1; fields_per_udt_warn_threshold=-1; file_cache_enabled=false; file_cache_round_up=null; file_cache_size=null; flush_compression=fast; force_new_prepared_statement_behaviour=false; full_query_logging_options=FullQueryLoggerOptions{log_dir='', archive_command='', roll_cycle='HOURLY', block=true, max_queue_weight=268435456, max_log_size=17179869184}; gc_log_threshold=200ms; gc_warn_threshold=1s; group_by_enabled=true; heap_dump_path=heapdump; hint_window_persistent_enabled=true; hinted_handoff_disabled_datacenters=[]; hinted_handoff_enabled=true; hinted_handoff_throttle=1024KiB; hints_compression=null; hints_directory=null; hints_flush_period=10000ms; ideal_consistency_level=null; in_select_cartesian_product_fail_threshold=-1; in_select_cartesian_product_warn_threshold=-1; incremental_backups=false; incremental_repair_disk_headroom_reject_ratio=0.2; index_summary_capacity=null; index_summary_resize_interval=60m; initial_location_provider=null; initial_range_tombstone_list_allocation_size=1; initial_token=null; inter_dc_stream_throughput_outbound=24MiB/s; inter_dc_tcp_nodelay=false; internode_application_receive_queue_capacity=4MiB; internode_application_receive_queue_reserve_endpoint_capacity=128MiB; internode_application_receive_queue_reserve_global_capacity=512MiB; internode_application_send_queue_capacity=4MiB; internode_application_send_queue_reserve_endpoint_capacity=128MiB; internode_application_send_queue_reserve_global_capacity=512MiB; internode_authenticator=null; internode_compression=dc; internode_error_reporting_exclusions=SubnetGroups{subnets=[]}; internode_max_message_size=null; internode_socket_receive_buffer_size=0B; internode_socket_send_buffer_size=0B; internode_streaming_tcp_user_timeout=300s; internode_tcp_connect_timeout=2s; internode_tcp_user_timeout=30s; internode_timeout=true; intersect_filtering_query_enabled=true; intersect_filtering_query_warned=true; invalid_legacy_protocol_magic_no_spam_enabled=false; items_per_collection_fail_threshold=-1; items_per_collection_warn_threshold=-1; jmx_server_options=null; key_cache_invalidate_after_sstable_deletion=false; key_cache_keys_to_save=**********; key_cache_migrate_during_compaction=true; key_cache_save_period=4h; key_cache_size=null; keyspaces_fail_threshold=-1; keyspaces_warn_threshold=-1; listen_address=localhost; listen_interface=null; listen_interface_prefer_ipv6=false; listen_on_broadcast_address=false; local_read_size_fail_threshold=null; local_read_size_warn_threshold=null; local_system_data_file_directory=null; materialized_views_enabled=false; materialized_views_on_repair_enabled=true; materialized_views_per_table_fail_threshold=-1; materialized_views_per_table_warn_threshold=-1; max_concurrent_automatic_sstable_upgrades=1; max_hint_window=3h; max_hints_delivery_threads=2; max_hints_file_size=128MiB; max_hints_size_per_host=0B; max_mutation_size=null; max_space_usable_for_compactions_in_percentage=0.95; max_streaming_retries=3; max_top_size_partition_count=10; max_top_tombstone_partition_count=10; max_value_size=256MiB; maximum_replication_factor_fail_threshold=-1; maximum_replication_factor_warn_threshold=-1; maximum_timestamp_fail_threshold=null; maximum_timestamp_warn_threshold=null; memtable=org.apache.cassandra.config.Config$MemtableOptions@6436a7db; memtable_allocation_type=heap_buffers; memtable_cleanup_threshold=null; memtable_flush_writers=0; memtable_heap_space=null; memtable_offheap_space=null; metadata_snapshot_frequency=100; min_free_space_per_drive=50MiB; min_tracked_partition_size=1MiB; min_tracked_partition_tombstone_count=5000; minimum_replication_factor_fail_threshold=-1; minimum_replication_factor_warn_threshold=-1; minimum_timestamp_fail_threshold=null; minimum_timestamp_warn_threshold=null; native_transport_allow_older_protocols=true; native_transport_flush_in_batches_legacy=false; native_transport_idle_timeout=0ms; native_transport_max_auth_threads=4; native_transport_max_backoff_on_queue_overload=200ms; native_transport_max_concurrent_connections=-1; native_transport_max_concurrent_connections_per_ip=-1; native_transport_max_frame_size=16MiB; native_transport_max_message_size=null; native_transport_max_request_data_in_flight=null; native_transport_max_request_data_in_flight_per_ip=null; native_transport_max_requests_per_second=1000000; native_transport_max_threads=128; native_transport_min_backoff_on_queue_overload=10ms; native_transport_port=9042; native_transport_queue_max_item_age_threshold=1.7976931348623157E308; native_transport_rate_limiting_enabled=false; native_transport_receive_queue_capacity=1MiB; native_transport_throw_on_overload=false; native_transport_timeout=12s; network_authorizer=AllowAllNetworkAuthorizernull; networking_cache_size=null; node_proximity=null; non_partition_restricted_index_query_enabled=true; num_tokens=16; otc_backlog_expiration_interval_ms=200; otc_coalescing_enough_coalesced_messages=8; otc_coalescing_strategy=DISABLED; otc_coalescing_window_us=200; page_size_fail_threshold=-1; page_size_warn_threshold=-1; partition_denylist_enabled=false; partition_keys_in_select_fail_threshold=-1; partition_keys_in_select_warn_threshold=-1; partition_size_fail_threshold=null; partition_size_warn_threshold=null; partition_tombstones_fail_threshold=-1; partition_tombstones_warn_threshold=-1; partitioner=org.apache.cassandra.dht.Murmur3Partitioner; password_validator={}; password_validator_reconfiguration_enabled=true; paxos_cache_size=null; paxos_contention_max_wait=null; paxos_contention_min_delta=null; paxos_contention_min_wait=null; paxos_contention_wait_randomizer=null; paxos_on_linearizability_violations=ignore; paxos_purge_grace_period=60s; paxos_repair_enabled=true; paxos_repair_parallelism=-1; paxos_repair_race_wait=true; paxos_state_purging=null; paxos_topology_repair_no_dc_checks=false; paxos_topology_repair_strict_each_quorum=false; paxos_variant=v1; periodic_commitlog_sync_lag_block=null; permissions_cache_active_update=false; permissions_cache_max_entries=1000; permissions_update_interval=null; permissions_validity=2000ms; phi_convict_threshold=8.0; prefer_local_connections=false; prepared_statements_cache_size=null; progress_barrier_backoff=1000ms; progress_barrier_default_consistency_level=EACH_QUORUM; progress_barrier_min_consistency_level=EACH_QUORUM; progress_barrier_timeout=3600000ms; range_request_timeout=10000ms; range_tombstone_list_growth_factor=1.5; read_before_write_list_operations_enabled=true; read_consistency_levels_disallowed=[]; read_consistency_levels_warned=[]; read_request_timeout=5000ms; read_thresholds_enabled=false; reject_repair_compaction_threshold=**********; repair_command_pool_full_strategy=queue; repair_command_pool_size=0; repair_request_timeout=120000ms; repair_session_max_tree_depth=null; repair_session_space=null; repair_state_expires=3d; repair_state_size=100000; repaired_data_tracking_for_partition_reads_enabled=false; repaired_data_tracking_for_range_reads_enabled=false; report_unconfirmed_repaired_data_mismatches=false; request_timeout=10000ms; role_manager=CassandraRoleManagernull; roles_cache_active_update=false; roles_cache_max_entries=1000; roles_update_interval=null; roles_validity=2000ms; row_cache_class_name=org.apache.cassandra.cache.OHCProvider; row_cache_keys_to_save=**********; row_cache_save_period=0s; row_cache_size=0MiB; row_index_read_size_fail_threshold=null; row_index_read_size_warn_threshold=null; rpc_address=localhost; rpc_interface=null; rpc_interface_prefer_ipv6=false; rpc_keepalive=true; sai_frozen_term_size_fail_threshold=8KiB; sai_frozen_term_size_warn_threshold=1KiB; sai_options=org.apache.cassandra.config.StorageAttachedIndexOptions@67304a40; sai_sstable_indexes_per_query_fail_threshold=-1; sai_sstable_indexes_per_query_warn_threshold=32; sai_string_term_size_fail_threshold=8KiB; sai_string_term_size_warn_threshold=1KiB; sai_vector_term_size_fail_threshold=32KiB; sai_vector_term_size_warn_threshold=16KiB; sasi_indexes_enabled=false; saved_caches_directory=null; scripted_user_defined_functions_enabled=false; secondary_indexes_enabled=true; secondary_indexes_per_table_fail_threshold=-1; secondary_indexes_per_table_warn_threshold=-1; seed_provider=org.apache.cassandra.locator.SimpleSeedProvider{seeds=127.0.0.1:7000}; server_encryption_options=<REDACTED>; severity_during_decommission=0.0; short_rpc_timeout=1000ms; simplestrategy_enabled=true; skip_paxos_repair_on_topology_change=false; skip_paxos_repair_on_topology_change_keyspaces=[]; skip_stream_disk_space_check=false; slow_query_log_timeout=500ms; snapshot_before_compaction=false; snapshot_links_per_second=0; snapshot_on_duplicate_row_detection=false; snapshot_on_repaired_data_mismatch=false; ssl_storage_port=7001; sstable_preemptive_open_interval=50MiB; sstable_read_rate_persistence_enabled=false; start_native_transport=true; startup_checks={}; storage_compatibility_mode=NONE; storage_port=7000; stream_entire_sstables=true; stream_throughput_outbound=24MiB/s; stream_transfer_task_timeout=12h; streaming_connections_per_host=1; streaming_keep_alive_period=300s; streaming_slow_events_log_timeout=10s; streaming_state_expires=3d; streaming_state_size=40MiB; streaming_stats_enabled=true; table_properties_disallowed=[]; table_properties_ignored=[]; table_properties_warned=[]; tables_fail_threshold=-1; tables_warn_threshold=-1; tombstone_failure_threshold=100000; tombstone_read_purgeable_metric_granularity=disabled; tombstone_warn_threshold=1000; top_partitions_enabled=true; trace_type_query_ttl=1d; trace_type_repair_ttl=7d; transfer_hints_on_decommission=true; transient_replication_enabled=false; transparent_data_encryption_options=org.apache.cassandra.config.TransparentDataEncryptionOptions@7776ab; traverse_auth_from_root=false; trickle_fsync=false; trickle_fsync_interval=10240KiB; triggers_policy=enabled; truncate_request_timeout=60000ms; uncompressed_tables_enabled=true; unlogged_batch_across_partitions_warn_threshold=10; unsafe_tcm_mode=false; use_creation_time_for_hint_ttl=true; use_deterministic_table_id=false; use_offheap_merkle_trees=true; use_statements_enabled=true; user_defined_functions_enabled=false; user_defined_functions_fail_timeout=1500ms; user_defined_functions_threads_enabled=true; user_defined_functions_warn_timeout=500ms; user_function_timeout_policy=die; user_timestamps_enabled=true; uuid_sstable_identifiers_enabled=false; validation_preview_purge_head_start=3600s; vector_dimensions_fail_threshold=-1; vector_dimensions_warn_threshold=-1; vector_type_enabled=true; windows_timer_interval=0; write_consistency_levels_disallowed=[]; write_consistency_levels_warned=[]; write_request_timeout=10000ms; zero_ttl_on_twcs_enabled=true; zero_ttl_on_twcs_warned=true]
INFO  [main] 2025-08-01T20:09:11,566 DatabaseDescriptor.java:1863 - Supported sstable formats are: big -> org.apache.cassandra.io.sstable.format.big.BigFormat with singleton components: [Data.db, Index.db, Statistics.db, CompressionInfo.db, Filter.db, Summary.db, Digest.crc32, CRC.db, TOC.txt], bti -> org.apache.cassandra.io.sstable.format.bti.BtiFormat with singleton components: [Data.db, Partitions.db, Rows.db, Statistics.db, CompressionInfo.db, Filter.db, Digest.crc32, CRC.db, TOC.txt]
INFO  [main] 2025-08-01T20:09:11,888 AbstractCryptoProvider.java:140 - AmazonCorrettoCryptoProvider health check OK.
INFO  [main] 2025-08-01T20:09:11,889 DatabaseDescriptor.java:648 - DiskAccessMode is standard, indexAccessMode is mmap
INFO  [main] 2025-08-01T20:09:11,890 DatabaseDescriptor.java:690 - Global memtable on-heap threshold is enabled at 2000MiB
INFO  [main] 2025-08-01T20:09:11,890 DatabaseDescriptor.java:694 - Global memtable off-heap threshold is enabled at 2000MiB
INFO  [main] 2025-08-01T20:09:11,908 DatabaseDescriptor.java:742 - commitlog_disk_access_mode resolved to: mmap
INFO  [main] 2025-08-01T20:09:11,909 DatabaseDescriptor.java:767 - Native transport rate-limiting disabled.
WARN  [main] 2025-08-01T20:09:11,917 DatabaseDescriptor.java:834 - Only 10.086GiB free across all data volumes. Consider adding more capacity to your cluster or removing obsolete snapshots
INFO  [main] 2025-08-01T20:09:12,363 DatabaseDescriptor.java:1635 - Use of endpoint_snitch in configuration is deprecated and should be replaced by initial_location_provider and node_proximity
INFO  [main] 2025-08-01T20:09:12,599 AuditLogManager.java:102 - Audit logging is disabled.
INFO  [main] 2025-08-01T20:09:12,605 JMXServerUtils.java:260 - Configured JMX server at: service:jmx:rmi://127.0.0.1/jndi/rmi://127.0.0.1:7199/jmxrmi
INFO  [main] 2025-08-01T20:09:12,616 CassandraDaemon.java:615 - Hostname: 399474ba19af:7000:7001
INFO  [main] 2025-08-01T20:09:12,617 CassandraDaemon.java:622 - JVM vendor/version: OpenJDK 64-Bit Server VM/11.0.28
INFO  [main] 2025-08-01T20:09:12,617 CassandraDaemon.java:623 - Heap size: 7.813GiB/7.813GiB
INFO  [main] 2025-08-01T20:09:12,622 CassandraDaemon.java:628 - CodeHeap 'non-nmethods' Non-heap memory: init = 2555904(2496K) used = 1425920(1392K) committed = 2555904(2496K) max = 5832704(5696K)
INFO  [main] 2025-08-01T20:09:12,625 CassandraDaemon.java:628 - Metaspace Non-heap memory: init = 0(0K) used = 25867280(25261K) committed = 27574272(26928K) max = -1(-1K)
INFO  [main] 2025-08-01T20:09:12,626 CassandraDaemon.java:628 - CodeHeap 'profiled nmethods' Non-heap memory: init = 2555904(2496K) used = 6107904(5964K) committed = 6160384(6016K) max = 122912768(120032K)
INFO  [main] 2025-08-01T20:09:12,626 CassandraDaemon.java:628 - Compressed Class Space Non-heap memory: init = 0(0K) used = 3295864(3218K) committed = 4063232(3968K) max = 1073741824(1048576K)
INFO  [main] 2025-08-01T20:09:12,626 CassandraDaemon.java:628 - G1 Eden Space Heap memory: init = 4412407808(4308992K) used = 184549376(180224K) committed = 4412407808(4308992K) max = -1(-1K)
INFO  [main] 2025-08-01T20:09:12,626 CassandraDaemon.java:628 - G1 Old Gen Heap memory: init = 3976200192(3883008K) used = 0(0K) committed = 3976200192(3883008K) max = 8388608000(8192000K)
INFO  [main] 2025-08-01T20:09:12,626 CassandraDaemon.java:628 - G1 Survivor Space Heap memory: init = 0(0K) used = 16777216(16384K) committed = 16777216(16384K) max = -1(-1K)
INFO  [main] 2025-08-01T20:09:12,626 CassandraDaemon.java:628 - CodeHeap 'non-profiled nmethods' Non-heap memory: init = 2555904(2496K) used = 1636352(1598K) committed = 2555904(2496K) max = 122912768(120032K)
INFO  [main] 2025-08-01T20:09:12,627 CassandraDaemon.java:630 - Classpath: bin/../conf:bin/../build/apache-cassandra-5.1-SNAPSHOT.jar:bin/../lib/HdrHistogram-2.1.12.jar:bin/../lib/ST4-4.0.8.jar:bin/../lib/affinity-3.23.3.jar:bin/../lib/agrona-1.17.1.jar:bin/../lib/airline-0.8.jar:bin/../lib/antlr-runtime-3.5.2.jar:bin/../lib/asm-9.4.jar:bin/../lib/big-math-2.3.0.jar:bin/../lib/caffeine-3.1.8.jar:bin/../lib/cassandra-accord-5.1-SNAPSHOT.jar:bin/../lib/cassandra-driver-core-3.12.1-shaded.jar:bin/../lib/chronicle-bytes-2.23.33.jar:bin/../lib/chronicle-core-2.23.36.jar:bin/../lib/chronicle-queue-5.23.37.jar:bin/../lib/chronicle-threads-2.23.25.jar:bin/../lib/chronicle-wire-2.23.39.jar:bin/../lib/commons-cli-1.5.0.jar:bin/../lib/commons-lang3-3.13.0.jar:bin/../lib/commons-math3-3.2.jar:bin/../lib/concurrent-trees-2.4.0.jar:bin/../lib/ecj-3.33.0.jar:bin/../lib/failureaccess-1.0.1.jar:bin/../lib/guava-32.0.1-jre.jar:bin/../lib/high-scale-lib-1.0.6.jar:bin/../lib/hppc-0.8.1.jar:bin/../lib/ipaddress-5.3.3.jar:bin/../lib/j2objc-annotations-1.3.jar:bin/../lib/jackson-annotations-2.15.3.jar:bin/../lib/jackson-core-2.15.3.jar:bin/../lib/jackson-databind-2.15.3.jar:bin/../lib/jackson-datatype-jsr310-2.15.3.jar:bin/../lib/jamm-0.4.0.jar:bin/../lib/javax.inject-1.jar:bin/../lib/jbcrypt-0.4.jar:bin/../lib/jcl-over-slf4j-2.0.17.jar:bin/../lib/jcommander-1.30.jar:bin/../lib/jctools-core-3.1.0.jar:bin/../lib/jffi-1.3.11-native.jar:bin/../lib/jffi-1.3.11.jar:bin/../lib/jna-5.13.0.jar:bin/../lib/jna-platform-5.13.0.jar:bin/../lib/jnr-a64asm-1.0.0.jar:bin/../lib/jnr-constants-0.10.4.jar:bin/../lib/jnr-ffi-2.2.13.jar:bin/../lib/jnr-x86asm-1.0.2.jar:bin/../lib/jsr305-2.0.2.jar:bin/../lib/jvector-1.0.2.jar:bin/../lib/jvm-attach-api-1.5.jar:bin/../lib/log4j-over-slf4j-2.0.17.jar:bin/../lib/logback-classic-1.5.18.jar:bin/../lib/logback-core-1.5.18.jar:bin/../lib/lucene-analysis-common-9.12.0.jar:bin/../lib/lucene-core-9.12.0.jar:bin/../lib/lz4-java-1.8.0.jar:bin/../lib/metrics-core-4.2.28.jar:bin/../lib/metrics-jvm-4.2.19.jar:bin/../lib/metrics-logback-4.2.19.jar:bin/../lib/mxdump-0.14.jar:bin/../lib/netty-all-4.1.119.Final.jar:bin/../lib/netty-buffer-4.1.119.Final.jar:bin/../lib/netty-codec-4.1.119.Final.jar:bin/../lib/netty-common-4.1.119.Final.jar:bin/../lib/netty-handler-4.1.119.Final.jar:bin/../lib/netty-handler-proxy-4.1.119.Final.jar:bin/../lib/netty-handler-ssl-ocsp-4.1.119.Final.jar:bin/../lib/netty-resolver-4.1.119.Final.jar:bin/../lib/netty-tcnative-boringssl-static-2.0.70.Final-linux-aarch_64.jar:bin/../lib/netty-tcnative-boringssl-static-2.0.70.Final-linux-x86_64.jar:bin/../lib/netty-tcnative-boringssl-static-2.0.70.Final-osx-aarch_64.jar:bin/../lib/netty-tcnative-boringssl-static-2.0.70.Final-osx-x86_64.jar:bin/../lib/netty-tcnative-boringssl-static-2.0.70.Final.jar:bin/../lib/netty-tcnative-classes-2.0.70.Final.jar:bin/../lib/netty-transport-4.1.119.Final.jar:bin/../lib/netty-transport-classes-epoll-4.1.119.Final.jar:bin/../lib/netty-transport-classes-kqueue-4.1.119.Final.jar:bin/../lib/netty-transport-native-epoll-4.1.119.Final-linux-aarch_64.jar:bin/../lib/netty-transport-native-epoll-4.1.119.Final-linux-x86_64.jar:bin/../lib/netty-transport-native-epoll-4.1.119.Final.jar:bin/../lib/netty-transport-native-unix-common-4.1.119.Final.jar:bin/../lib/ohc-core-0.5.1.jar:bin/../lib/ohc-core-j8-0.5.1.jar:bin/../lib/oshi-core-6.4.8.jar:bin/../lib/passay-1.6.4.jar:bin/../lib/posix-2.24ea4.jar:bin/../lib/psjava-0.1.19.jar:bin/../lib/semver4j-3.1.0.jar:bin/../lib/sjk-cli-0.14.jar:bin/../lib/sjk-core-0.14.jar:bin/../lib/sjk-json-0.14.jar:bin/../lib/sjk-stacktrace-0.14.jar:bin/../lib/slf4j-api-2.0.17.jar:bin/../lib/snakeyaml-2.1.jar:bin/../lib/snappy-java-********.jar:bin/../lib/stream-2.5.2.jar:bin/../lib/zstd-jni-1.5.7-2.jar:bin/../lib/jsr223/*/*.jar::bin/../lib/x86_64/AmazonCorrettoCryptoProvider-2.2.0-linux-x86_64.jar
INFO  [main] 2025-08-01T20:09:12,628 CassandraDaemon.java:632 - JVM Arguments: [-ea, -da:net.openhft..., -XX:+UseThreadPriorities, -XX:+HeapDumpOnOutOfMemoryError, -Xss256k, -XX:+AlwaysPreTouch, -XX:+UseTLAB, -XX:+ResizeTLAB, -XX:+UseNUMA, -XX:+PerfDisableSharedMem, -Djava.net.preferIPv4Stack=true, -Dchronicle.analytics.disable=true, -XX:-UseBiasedLocking, -XX:+UseG1GC, -XX:+ParallelRefProcEnabled, -XX:MaxTenuringThreshold=2, -XX:G1HeapRegionSize=16m, -XX:+UnlockExperimentalVMOptions, -XX:G1NewSizePercent=50, -XX:G1RSetUpdatingPauseTimePercent=5, -XX:MaxGCPauseMillis=300, -XX:InitiatingHeapOccupancyPercent=70, -Djdk.attach.allowAttachSelf=true, --add-exports=java.base/jdk.internal.misc=ALL-UNNAMED, --add-exports=java.base/jdk.internal.ref=ALL-UNNAMED, --add-exports=java.base/jdk.internal.util=ALL-UNNAMED, --add-exports=java.base/sun.nio.ch=ALL-UNNAMED, --add-exports=java.management.rmi/com.sun.jmx.remote.internal.rmi=ALL-UNNAMED, --add-exports=java.rmi/sun.rmi.registry=ALL-UNNAMED, --add-exports=java.rmi/sun.rmi.server=ALL-UNNAMED, --add-exports=java.sql/java.sql=ALL-UNNAMED, --add-opens=java.base/java.lang.module=ALL-UNNAMED, --add-opens=java.base/jdk.internal.loader=ALL-UNNAMED, --add-opens=java.base/jdk.internal.ref=ALL-UNNAMED, --add-opens=java.base/jdk.internal.reflect=ALL-UNNAMED, --add-opens=java.base/jdk.internal.math=ALL-UNNAMED, --add-opens=java.base/jdk.internal.module=ALL-UNNAMED, --add-opens=java.base/jdk.internal.util.jar=ALL-UNNAMED, --add-opens=jdk.management/com.sun.management.internal=ALL-UNNAMED, -Dio.netty.tryReflectionSetAccessible=true, -Dio.netty.allocator.useCacheForAllThreads=true, -Dio.netty.allocator.maxOrder=11, -Xlog:gc=info,heap*=trace,age*=debug,safepoint=info,promotion*=trace:file=bin/../logs/gc.log:time,uptime,pid,tid,level:filecount=10,filesize=10485760, -Xms7997M, -Xmx7997M, -XX:MaxDirectMemorySize=3998M, -XX:ParallelGCThreads=4, -XX:ConcGCThreads=4, -XX:CompileCommandFile=bin/../conf/hotspot_compiler, -javaagent:bin/../lib/jamm-0.4.0.jar, -XX:HeapDumpPath=bin/../logs/cassandra-1754078948-pid73268.hprof, -Dcassandra.jmx.local.port=7199, -Dcom.sun.management.jmxremote.authenticate=false, -Dcom.sun.management.jmxremote.password.file=/etc/cassandra/jmxremote.password, -XX:OnOutOfMemoryError=kill -9 %p, -Dlogback.configurationFile=logback.xml, -Dcassandra.logdir=bin/../logs, -Dcassandra.storagedir=bin/../data]
WARN  [main] 2025-08-01T20:09:12,686 NativeLibrary.java:200 - Unable to lock JVM memory (ENOMEM). This can result in part of the JVM being swapped out, especially with mmapped I/O enabled. Increase RLIMIT_MEMLOCK.
INFO  [main] 2025-08-01T20:09:12,898 MonotonicClock.java:208 - Scheduling approximate time conversion task with an interval of 10000 milliseconds
INFO  [main] 2025-08-01T20:09:12,899 MonotonicClock.java:351 - Scheduling approximate time-check task with a precision of 2 milliseconds
INFO  [main] 2025-08-01T20:09:13,529 SkipListMemtable.java:249 - Estimated SkipListMemtable row overhead: 172
INFO  [main] 2025-08-01T20:09:13,884 SnapshotManager.java:217 - Scheduling expired snapshots cleanup with initialDelaySeconds=5 and cleanupPeriodSeconds=60
WARN  [main] 2025-08-01T20:09:13,886 StartupChecks.java:258 - jemalloc shared library could not be preloaded to speed up memory allocations
WARN  [main] 2025-08-01T20:09:13,886 StartupChecks.java:322 - JMX is not enabled to receive remote connections. Please see jmx_server_options in cassandra.yaml for more info.
WARN  [main] 2025-08-01T20:09:13,941 StartupChecks.java:430 - Cassandra server running in degraded mode. Swap should be disabled. 
WARN  [main] 2025-08-01T20:09:13,972 StartupChecks.java:558 - Maximum number of memory map areas per process (vm.max_map_count) 262144 is too low, recommended value: 1048575, you can change it with sysctl.
INFO  [main] 2025-08-01T20:09:14,074 ColumnFamilyStore.java:497 - Initializing system.IndexInfo
INFO  [main] 2025-08-01T20:09:14,172 ColumnFamilyStore.java:497 - Initializing system.batches
INFO  [main] 2025-08-01T20:09:14,188 ColumnFamilyStore.java:497 - Initializing system.paxos
INFO  [main] 2025-08-01T20:09:14,196 SecondaryIndexManager.java:228 - Index [PaxosUncommittedIndex] registered and writable.
INFO  [SecondaryIndexManagement:1] 2025-08-01T20:09:14,212 SecondaryIndexManager.java:1877 - Index [PaxosUncommittedIndex] became queryable after successful build.
INFO  [main] 2025-08-01T20:09:14,223 ColumnFamilyStore.java:497 - Initializing system.paxos_repair_history
INFO  [main] 2025-08-01T20:09:14,236 ColumnFamilyStore.java:497 - Initializing system.local
INFO  [main] 2025-08-01T20:09:14,247 ColumnFamilyStore.java:497 - Initializing system.peers_v2
INFO  [SecondaryIndexManagement:1] 2025-08-01T20:09:14,255 QueryProcessor.java:157 - Initialized prepared statement caches with 31 MiB
INFO  [main] 2025-08-01T20:09:14,256 ColumnFamilyStore.java:497 - Initializing system.peers
INFO  [main] 2025-08-01T20:09:14,265 ColumnFamilyStore.java:497 - Initializing system.peer_events_v2
INFO  [main] 2025-08-01T20:09:14,273 ColumnFamilyStore.java:497 - Initializing system.peer_events
INFO  [main] 2025-08-01T20:09:14,280 ColumnFamilyStore.java:497 - Initializing system.compaction_history
INFO  [main] 2025-08-01T20:09:14,290 ColumnFamilyStore.java:497 - Initializing system.sstable_activity
INFO  [main] 2025-08-01T20:09:14,298 ColumnFamilyStore.java:497 - Initializing system.sstable_activity_v2
INFO  [main] 2025-08-01T20:09:14,307 ColumnFamilyStore.java:497 - Initializing system.size_estimates
INFO  [main] 2025-08-01T20:09:14,314 ColumnFamilyStore.java:497 - Initializing system.table_estimates
INFO  [main] 2025-08-01T20:09:14,325 ColumnFamilyStore.java:497 - Initializing system.available_ranges_v2
INFO  [main] 2025-08-01T20:09:14,334 ColumnFamilyStore.java:497 - Initializing system.available_ranges
INFO  [main] 2025-08-01T20:09:14,342 ColumnFamilyStore.java:497 - Initializing system.transferred_ranges_v2
INFO  [main] 2025-08-01T20:09:14,351 ColumnFamilyStore.java:497 - Initializing system.transferred_ranges
INFO  [main] 2025-08-01T20:09:14,361 ColumnFamilyStore.java:497 - Initializing system.view_builds_in_progress
INFO  [main] 2025-08-01T20:09:14,369 ColumnFamilyStore.java:497 - Initializing system.built_views
INFO  [main] 2025-08-01T20:09:14,376 ColumnFamilyStore.java:497 - Initializing system.prepared_statements
INFO  [main] 2025-08-01T20:09:14,381 ColumnFamilyStore.java:497 - Initializing system.repairs
INFO  [main] 2025-08-01T20:09:14,388 ColumnFamilyStore.java:497 - Initializing system.top_partitions
INFO  [main] 2025-08-01T20:09:14,395 ColumnFamilyStore.java:497 - Initializing system.local_metadata_log
INFO  [main] 2025-08-01T20:09:14,403 ColumnFamilyStore.java:497 - Initializing system.metadata_snapshots
INFO  [main] 2025-08-01T20:09:14,411 ColumnFamilyStore.java:497 - Initializing system.consensus_migration_state
INFO  [main] 2025-08-01T20:09:14,468 ColumnFamilyStore.java:497 - Initializing system_schema.keyspaces
INFO  [SecondaryIndexManagement:1] 2025-08-01T20:09:14,473 ColumnFamilyStore.java:1052 - Enqueuing flush of system.IndexInfo, Reason: INTERNALLY_FORCED, Usage: 463B (0%) on-heap, 0B (0%) off-heap
INFO  [main] 2025-08-01T20:09:14,479 ColumnFamilyStore.java:497 - Initializing system_schema.tables
INFO  [main] 2025-08-01T20:09:14,485 ColumnFamilyStore.java:497 - Initializing system_schema.columns
INFO  [main] 2025-08-01T20:09:14,493 ColumnFamilyStore.java:497 - Initializing system_schema.column_masks
INFO  [main] 2025-08-01T20:09:14,502 ColumnFamilyStore.java:497 - Initializing system_schema.triggers
INFO  [main] 2025-08-01T20:09:14,513 ColumnFamilyStore.java:497 - Initializing system_schema.dropped_columns
INFO  [main] 2025-08-01T20:09:14,525 ColumnFamilyStore.java:497 - Initializing system_schema.views
INFO  [main] 2025-08-01T20:09:14,536 ColumnFamilyStore.java:497 - Initializing system_schema.types
INFO  [main] 2025-08-01T20:09:14,545 ColumnFamilyStore.java:497 - Initializing system_schema.functions
INFO  [main] 2025-08-01T20:09:14,556 ColumnFamilyStore.java:497 - Initializing system_schema.aggregates
INFO  [main] 2025-08-01T20:09:14,566 ColumnFamilyStore.java:497 - Initializing system_schema.indexes
INFO  [main] 2025-08-01T20:09:14,577 FileSystemOwnershipCheck.java:122 - Filesystem ownership check is not enabled.
INFO  [PerDiskMemtableFlushWriter_0:1] 2025-08-01T20:09:14,615 Flushing.java:153 - Writing Memtable-IndexInfo@1213152561(50B serialized bytes, 1 ops, 463B (0%) on-heap, 0B (0%) off-heap), flushed range = [null, null)
INFO  [PerDiskMemtableFlushWriter_0:1] 2025-08-01T20:09:14,621 Flushing.java:179 - Completed flushing /testbed/data/data/system/IndexInfo-9f5c6374d48532299a0a5094af9ad1e3/oa-1-big-Data.db (37B) for commitlog position CommitLogPosition(segmentId=1754078953681, position=104)
INFO  [MemtableFlushWriter:1] 2025-08-01T20:09:14,674 CacheService.java:121 - Initializing key cache with capacity of 100 MiBs.
INFO  [MemtableFlushWriter:1] 2025-08-01T20:09:14,682 CacheService.java:143 - Initializing row cache with capacity of 0 MiBs
INFO  [MemtableFlushWriter:1] 2025-08-01T20:09:14,684 CacheService.java:172 - Initializing counter cache with capacity of 50 MiBs
INFO  [MemtableFlushWriter:1] 2025-08-01T20:09:14,685 CacheService.java:183 - Scheduling counter cache save to every 7200 seconds (going to save all keys).
INFO  [MemtableFlushWriter:1] 2025-08-01T20:09:14,692 LogTransaction.java:254 - Unfinished transaction log, deleting /testbed/data/data/system/IndexInfo-9f5c6374d48532299a0a5094af9ad1e3/oa_txn_flush_6567d250-6f13-11f0-b119-c19ab67cbe2a.log 
INFO  [main] 2025-08-01T20:09:14,857 Startup.java:572 - hasAnyEpoch = false, hasBootedBefore = false
INFO  [main] 2025-08-01T20:09:14,858 Startup.java:103 - Initializing as first CMS node in a new cluster
INFO  [main] 2025-08-01T20:09:15,033 RegistrationStatus.java:51 - Node is initialized, moving to UNREGISTERED state
INFO  [main] 2025-08-01T20:09:15,092 StorageService.java:511 - Initializing storage service mbean
INFO  [main] 2025-08-01T20:09:15,109 StreamManager.java:268 - Storing streaming state for 3d or for size 41943040
INFO  [main] 2025-08-01T20:09:15,123 LocalLog.java:682 - Marking LocalLog ready at epoch Epoch{epoch=0}
INFO  [main] 2025-08-01T20:09:15,124 LocalLog.java:690 - Adding default listeners to LocalLog
INFO  [main] 2025-08-01T20:09:15,134 LocalLog.java:701 - Notifying all registered listeners of both pre and post commit event
INFO  [main] 2025-08-01T20:09:15,135 LocalLog.java:628 - Notifying listeners, prev epoch = Epoch{epoch=0}, current epoch = Epoch{epoch=0}
INFO  [main] 2025-08-01T20:09:15,140 ColumnFamilyStore.java:497 - Initializing system_cluster_metadata.distributed_metadata_log
INFO  [main] 2025-08-01T20:09:15,168 ColumnFamilyStore.java:1052 - Enqueuing flush of system_schema.columns, Reason: INTERNALLY_FORCED, Usage: 3.295KiB (0%) on-heap, 0B (0%) off-heap
INFO  [PerDiskMemtableFlushWriter_0:2] 2025-08-01T20:09:15,174 Flushing.java:153 - Writing Memtable-columns@1981903033(735B serialized bytes, 4 ops, 3.295KiB (0%) on-heap, 0B (0%) off-heap), flushed range = [null, null)
INFO  [PerDiskMemtableFlushWriter_0:2] 2025-08-01T20:09:15,176 Flushing.java:179 - Completed flushing /testbed/data/data/system_schema/columns-24101c25a2ae3af787c1b40ee1aca33f/oa-1-big-Data.db (334B) for commitlog position CommitLogPosition(segmentId=1754078953681, position=1587)
INFO  [MemtableFlushWriter:2] 2025-08-01T20:09:15,185 LogTransaction.java:254 - Unfinished transaction log, deleting /testbed/data/data/system_schema/columns-24101c25a2ae3af787c1b40ee1aca33f/oa_txn_flush_65d03110-6f13-11f0-b119-c19ab67cbe2a.log 
INFO  [main] 2025-08-01T20:09:15,187 ColumnFamilyStore.java:1052 - Enqueuing flush of system_schema.tables, Reason: INTERNALLY_FORCED, Usage: 2.787KiB (0%) on-heap, 0B (0%) off-heap
INFO  [PerDiskMemtableFlushWriter_0:1] 2025-08-01T20:09:15,192 Flushing.java:153 - Writing Memtable-tables@1436085036(868B serialized bytes, 1 ops, 2.787KiB (0%) on-heap, 0B (0%) off-heap), flushed range = [null, null)
INFO  [PerDiskMemtableFlushWriter_0:1] 2025-08-01T20:09:15,193 Flushing.java:179 - Completed flushing /testbed/data/data/system_schema/tables-afddfb9dbc1e30688056eed6c302ba09/oa-1-big-Data.db (524B) for commitlog position CommitLogPosition(segmentId=1754078953681, position=1595)
INFO  [MemtableFlushWriter:1] 2025-08-01T20:09:15,206 LogTransaction.java:254 - Unfinished transaction log, deleting /testbed/data/data/system_schema/tables-afddfb9dbc1e30688056eed6c302ba09/oa_txn_flush_65d31740-6f13-11f0-b119-c19ab67cbe2a.log 
INFO  [main] 2025-08-01T20:09:15,208 ColumnFamilyStore.java:1052 - Enqueuing flush of system_schema.keyspaces, Reason: INTERNALLY_FORCED, Usage: 816B (0%) on-heap, 0B (0%) off-heap
INFO  [PerDiskMemtableFlushWriter_0:2] 2025-08-01T20:09:15,213 Flushing.java:153 - Writing Memtable-keyspaces@2028376077(190B serialized bytes, 1 ops, 816B (0%) on-heap, 0B (0%) off-heap), flushed range = [null, null)
INFO  [PerDiskMemtableFlushWriter_0:2] 2025-08-01T20:09:15,214 Flushing.java:179 - Completed flushing /testbed/data/data/system_schema/keyspaces-abac5682dea631c5b535b3d6cffd0fb6/oa-1-big-Data.db (137B) for commitlog position CommitLogPosition(segmentId=1754078953681, position=1595)
INFO  [MemtableFlushWriter:2] 2025-08-01T20:09:15,224 LogTransaction.java:254 - Unfinished transaction log, deleting /testbed/data/data/system_schema/keyspaces-abac5682dea631c5b535b3d6cffd0fb6/oa_txn_flush_65d64b90-6f13-11f0-b119-c19ab67cbe2a.log 
INFO  [GlobalLogFollower] 2025-08-01T20:09:15,288 ColumnFamilyStore.java:1052 - Enqueuing flush of system.local_metadata_log, Reason: INTERNALLY_FORCED, Usage: 734B (0%) on-heap, 0B (0%) off-heap
INFO  [PerDiskMemtableFlushWriter_0:1] 2025-08-01T20:09:15,294 Flushing.java:153 - Writing Memtable-local_metadata_log@920340017(123B serialized bytes, 1 ops, 734B (0%) on-heap, 0B (0%) off-heap), flushed range = [null, null)
INFO  [PerDiskMemtableFlushWriter_0:1] 2025-08-01T20:09:15,295 Flushing.java:179 - Completed flushing /testbed/data/data/system/local_metadata_log-e4ddd16ebea631f48361e88f07af8d4e/oa-1-big-Data.db (54B) for commitlog position CommitLogPosition(segmentId=1754078953681, position=1809)
INFO  [MemtableFlushWriter:1] 2025-08-01T20:09:15,303 LogTransaction.java:254 - Unfinished transaction log, deleting /testbed/data/data/system/local_metadata_log-e4ddd16ebea631f48361e88f07af8d4e/oa_txn_flush_65e28090-6f13-11f0-b119-c19ab67cbe2a.log 
INFO  [GlobalLogFollower] 2025-08-01T20:09:15,312 ColumnFamilyStore.java:1052 - Enqueuing flush of system_schema.keyspaces, Reason: INTERNALLY_FORCED, Usage: 827B (0%) on-heap, 0B (0%) off-heap
INFO  [PerDiskMemtableFlushWriter_0:2] 2025-08-01T20:09:15,319 Flushing.java:153 - Writing Memtable-keyspaces@2094603209(201B serialized bytes, 1 ops, 827B (0%) on-heap, 0B (0%) off-heap), flushed range = [null, null)
INFO  [PerDiskMemtableFlushWriter_0:2] 2025-08-01T20:09:15,321 Flushing.java:179 - Completed flushing /testbed/data/data/system_schema/keyspaces-abac5682dea631c5b535b3d6cffd0fb6/oa-2-big-Data.db (148B) for commitlog position CommitLogPosition(segmentId=1754078953681, position=2033)
INFO  [MemtableFlushWriter:2] 2025-08-01T20:09:15,331 LogTransaction.java:254 - Unfinished transaction log, deleting /testbed/data/data/system_schema/keyspaces-abac5682dea631c5b535b3d6cffd0fb6/oa_txn_flush_65e65120-6f13-11f0-b119-c19ab67cbe2a.log 
INFO  [GlobalLogFollower] 2025-08-01T20:09:15,334 LocalLog.java:541 - Enacted PreInitialize. New tail is Epoch{epoch=1}
INFO  [main] 2025-08-01T20:09:15,542 AbstractLocalProcessor.java:105 - Committed Initialize{baseState = Epoch{epoch=1}}. New epoch is Epoch{epoch=2}
INFO  [GlobalLogFollower] 2025-08-01T20:09:15,545 ColumnFamilyStore.java:1052 - Enqueuing flush of system.local_metadata_log, Reason: INTERNALLY_FORCED, Usage: 2.051KiB (0%) on-heap, 0B (0%) off-heap
INFO  [PerDiskMemtableFlushWriter_0:1] 2025-08-01T20:09:15,551 Flushing.java:153 - Writing Memtable-local_metadata_log@1106816415(1.454KiB serialized bytes, 1 ops, 2.051KiB (0%) on-heap, 0B (0%) off-heap), flushed range = [null, null)
INFO  [PerDiskMemtableFlushWriter_0:1] 2025-08-01T20:09:15,552 Flushing.java:179 - Completed flushing /testbed/data/data/system/local_metadata_log-e4ddd16ebea631f48361e88f07af8d4e/oa-2-big-Data.db (1.389KiB) for commitlog position CommitLogPosition(segmentId=1754078953681, position=9263)
INFO  [MemtableFlushWriter:1] 2025-08-01T20:09:15,561 LogTransaction.java:254 - Unfinished transaction log, deleting /testbed/data/data/system/local_metadata_log-e4ddd16ebea631f48361e88f07af8d4e/oa_txn_flush_6609b7a0-6f13-11f0-b119-c19ab67cbe2a.log 
INFO  [GlobalLogFollower] 2025-08-01T20:09:15,566 ColumnFamilyStore.java:497 - Initializing system_auth.roles
INFO  [GlobalLogFollower] 2025-08-01T20:09:15,575 ColumnFamilyStore.java:497 - Initializing system_auth.role_members
INFO  [GlobalLogFollower] 2025-08-01T20:09:15,582 ColumnFamilyStore.java:497 - Initializing system_auth.role_permissions
INFO  [GlobalLogFollower] 2025-08-01T20:09:15,588 ColumnFamilyStore.java:497 - Initializing system_auth.resource_role_permissons_index
INFO  [GlobalLogFollower] 2025-08-01T20:09:15,595 ColumnFamilyStore.java:497 - Initializing system_auth.network_permissions
INFO  [GlobalLogFollower] 2025-08-01T20:09:15,602 ColumnFamilyStore.java:497 - Initializing system_auth.cidr_permissions
INFO  [GlobalLogFollower] 2025-08-01T20:09:15,609 ColumnFamilyStore.java:497 - Initializing system_auth.cidr_groups
INFO  [GlobalLogFollower] 2025-08-01T20:09:15,616 ColumnFamilyStore.java:497 - Initializing system_auth.identity_to_role
INFO  [GlobalLogFollower] 2025-08-01T20:09:15,632 ColumnFamilyStore.java:497 - Initializing system_distributed.repair_history
INFO  [GlobalLogFollower] 2025-08-01T20:09:15,640 ColumnFamilyStore.java:497 - Initializing system_distributed.parent_repair_history
INFO  [GlobalLogFollower] 2025-08-01T20:09:15,648 ColumnFamilyStore.java:497 - Initializing system_distributed.view_build_status
INFO  [GlobalLogFollower] 2025-08-01T20:09:15,654 ColumnFamilyStore.java:497 - Initializing system_distributed.partition_denylist
INFO  [GlobalLogFollower] 2025-08-01T20:09:15,661 ColumnFamilyStore.java:497 - Initializing system_distributed.auto_repair_history
INFO  [GlobalLogFollower] 2025-08-01T20:09:15,666 ColumnFamilyStore.java:497 - Initializing system_distributed.auto_repair_priority
INFO  [GlobalLogFollower] 2025-08-01T20:09:15,675 ColumnFamilyStore.java:497 - Initializing system_traces.sessions
INFO  [GlobalLogFollower] 2025-08-01T20:09:15,681 ColumnFamilyStore.java:497 - Initializing system_traces.events
INFO  [GlobalLogFollower] 2025-08-01T20:09:15,716 ColumnFamilyStore.java:1052 - Enqueuing flush of system_schema.columns, Reason: INTERNALLY_FORCED, Usage: 58.230KiB (0%) on-heap, 0B (0%) off-heap
INFO  [PerDiskMemtableFlushWriter_0:2] 2025-08-01T20:09:15,730 Flushing.java:153 - Writing Memtable-columns@970693174(13.652KiB serialized bytes, 78 ops, 58.230KiB (0%) on-heap, 0B (0%) off-heap), flushed range = [min(-9223372036854775808), max(9223372036854775807))
INFO  [PerDiskMemtableFlushWriter_0:2] 2025-08-01T20:09:15,737 Flushing.java:179 - Completed flushing /testbed/data/data/system_schema/columns-24101c25a2ae3af787c1b40ee1aca33f/oa-2-big-Data.db (5.684KiB) for commitlog position CommitLogPosition(segmentId=1754078953681, position=24113)
INFO  [MemtableFlushWriter:2] 2025-08-01T20:09:15,747 LogTransaction.java:254 - Unfinished transaction log, deleting /testbed/data/data/system_schema/columns-24101c25a2ae3af787c1b40ee1aca33f/oa_txn_flush_66252ee0-6f13-11f0-b119-c19ab67cbe2a.log 
INFO  [GlobalLogFollower] 2025-08-01T20:09:15,750 ColumnFamilyStore.java:1052 - Enqueuing flush of system_schema.tables, Reason: INTERNALLY_FORCED, Usage: 38.676KiB (0%) on-heap, 0B (0%) off-heap
INFO  [PerDiskMemtableFlushWriter_0:1] 2025-08-01T20:09:15,754 Flushing.java:153 - Writing Memtable-tables@196666939(12.826KiB serialized bytes, 16 ops, 38.676KiB (0%) on-heap, 0B (0%) off-heap), flushed range = [min(-9223372036854775808), max(9223372036854775807))
INFO  [PerDiskMemtableFlushWriter_0:1] 2025-08-01T20:09:15,757 Flushing.java:179 - Completed flushing /testbed/data/data/system_schema/tables-afddfb9dbc1e30688056eed6c302ba09/oa-2-big-Data.db (7.183KiB) for commitlog position CommitLogPosition(segmentId=1754078953681, position=24113)
INFO  [MemtableFlushWriter:2] 2025-08-01T20:09:15,765 LogTransaction.java:254 - Unfinished transaction log, deleting /testbed/data/data/system_schema/tables-afddfb9dbc1e30688056eed6c302ba09/oa_txn_flush_6628ff70-6f13-11f0-b119-c19ab67cbe2a.log 
INFO  [GlobalLogFollower] 2025-08-01T20:09:15,769 ColumnFamilyStore.java:1052 - Enqueuing flush of system_schema.keyspaces, Reason: INTERNALLY_FORCED, Usage: 2.391KiB (0%) on-heap, 0B (0%) off-heap
INFO  [PerDiskMemtableFlushWriter_0:2] 2025-08-01T20:09:15,773 Flushing.java:153 - Writing Memtable-keyspaces@145229673(597B serialized bytes, 3 ops, 2.391KiB (0%) on-heap, 0B (0%) off-heap), flushed range = [min(-9223372036854775808), max(9223372036854775807))
INFO  [PerDiskMemtableFlushWriter_0:2] 2025-08-01T20:09:15,774 Flushing.java:179 - Completed flushing /testbed/data/data/system_schema/keyspaces-abac5682dea631c5b535b3d6cffd0fb6/oa-3-big-Data.db (411B) for commitlog position CommitLogPosition(segmentId=1754078953681, position=24113)
INFO  [MemtableFlushWriter:1] 2025-08-01T20:09:15,784 LogTransaction.java:254 - Unfinished transaction log, deleting /testbed/data/data/system_schema/keyspaces-abac5682dea631c5b535b3d6cffd0fb6/oa_txn_flush_662be5a0-6f13-11f0-b119-c19ab67cbe2a.log 
INFO  [GlobalLogFollower] 2025-08-01T20:09:15,785 LocalLog.java:541 - Enacted EXECUTED {Initialize{baseState = Epoch{epoch=1}}}. New tail is Epoch{epoch=2}
INFO  [main] 2025-08-01T20:09:15,795 InboundConnectionInitiator.java:165 - Listening on address: (localhost/127.0.0.1:7000), nic: lo, encryption: unencrypted
INFO  [main] 2025-08-01T20:09:15,814 BufferPools.java:49 - Global buffer pool limit is 512.000MiB for chunk-cache and 128.000MiB for networking
INFO  [main] 2025-08-01T20:09:15,858 SystemKeyspaceMigrator41.java:213 - peers_v2 table was empty, migrating legacy peers, if this fails you should fix the issue and then truncate peers_v2 to have it try again.
INFO  [main] 2025-08-01T20:09:15,859 SystemKeyspaceMigrator41.java:225 - Migrating rows from legacy peers to peers_v2
INFO  [main] 2025-08-01T20:09:15,859 SystemKeyspaceMigrator41.java:234 - Migrated 0 rows from legacy peers to peers_v2
INFO  [main] 2025-08-01T20:09:15,860 SystemKeyspaceMigrator41.java:213 - peer_events_v2 table was empty, migrating legacy peer_events, if this fails you should fix the issue and then truncate peer_events_v2 to have it try again.
INFO  [main] 2025-08-01T20:09:15,861 SystemKeyspaceMigrator41.java:225 - Migrating rows from legacy peer_events to peer_events_v2
INFO  [main] 2025-08-01T20:09:15,861 SystemKeyspaceMigrator41.java:234 - Migrated 0 rows from legacy peer_events to peer_events_v2
INFO  [main] 2025-08-01T20:09:15,861 SystemKeyspaceMigrator41.java:213 - transferred_ranges_v2 table was empty, migrating legacy transferred_ranges, if this fails you should fix the issue and then truncate transferred_ranges_v2 to have it try again.
INFO  [main] 2025-08-01T20:09:15,862 SystemKeyspaceMigrator41.java:225 - Migrating rows from legacy transferred_ranges to transferred_ranges_v2
INFO  [main] 2025-08-01T20:09:15,862 SystemKeyspaceMigrator41.java:234 - Migrated 0 rows from legacy transferred_ranges to transferred_ranges_v2
INFO  [main] 2025-08-01T20:09:15,863 SystemKeyspaceMigrator41.java:213 - available_ranges_v2 table was empty, migrating legacy available_ranges, if this fails you should fix the issue and then truncate available_ranges_v2 to have it try again.
INFO  [main] 2025-08-01T20:09:15,864 SystemKeyspaceMigrator41.java:225 - Migrating rows from legacy available_ranges to available_ranges_v2
INFO  [main] 2025-08-01T20:09:15,864 SystemKeyspaceMigrator41.java:234 - Migrated 0 rows from legacy available_ranges to available_ranges_v2
INFO  [main] 2025-08-01T20:09:15,864 ColumnFamilyStore.java:2476 - Truncating system.sstable_activity_v2
INFO  [main] 2025-08-01T20:09:15,869 ColumnFamilyStore.java:2510 - Truncating system.sstable_activity_v2 with truncatedAt=1754078955866
INFO  [main] 2025-08-01T20:09:15,873 ActiveRepairService.java:268 - Storing repair state for 3d or for 100000 elements
INFO  [main] 2025-08-01T20:09:15,882 ColumnFamilyStore.java:1052 - Enqueuing flush of system.local, Reason: INTERNALLY_FORCED, Usage: 2.092KiB (0%) on-heap, 0B (0%) off-heap
INFO  [PerDiskMemtableFlushWriter_0:1] 2025-08-01T20:09:15,887 Flushing.java:153 - Writing Memtable-local@426487275(506B serialized bytes, 4 ops, 2.092KiB (0%) on-heap, 0B (0%) off-heap), flushed range = [min(-9223372036854775808), max(9223372036854775807))
INFO  [PerDiskMemtableFlushWriter_0:1] 2025-08-01T20:09:15,888 Flushing.java:179 - Completed flushing /testbed/data/data/system/local-7ad54392bcdd35a684174e047860b377/oa-1-big-Data.db (215B) for commitlog position CommitLogPosition(segmentId=1754078953681, position=24673)
INFO  [MemtableFlushWriter:1] 2025-08-01T20:09:15,895 LogTransaction.java:254 - Unfinished transaction log, deleting /testbed/data/data/system/local-7ad54392bcdd35a684174e047860b377/oa_txn_flush_663d4ac0-6f13-11f0-b119-c19ab67cbe2a.log 
INFO  [main] 2025-08-01T20:09:15,898 ColumnFamilyStore.java:2534 - Truncate of system.sstable_activity_v2 is complete
INFO  [main] 2025-08-01T20:09:15,899 SystemKeyspaceMigrator41.java:213 - sstable_activity_v2 table was empty, migrating legacy sstable_activity, if this fails you should fix the issue and then truncate sstable_activity_v2 to have it try again.
INFO  [main] 2025-08-01T20:09:15,900 SystemKeyspaceMigrator41.java:225 - Migrating rows from legacy sstable_activity to sstable_activity_v2
INFO  [main] 2025-08-01T20:09:15,900 SystemKeyspaceMigrator41.java:234 - Migrated 0 rows from legacy sstable_activity to sstable_activity_v2
INFO  [main] 2025-08-01T20:09:15,901 SystemKeyspaceMigrator41.java:213 - compaction_history table was empty, migrating legacy compaction_history, if this fails you should fix the issue and then truncate compaction_history to have it try again.
INFO  [main] 2025-08-01T20:09:15,902 SystemKeyspaceMigrator41.java:225 - Migrating rows from legacy compaction_history to compaction_history
INFO  [main] 2025-08-01T20:09:15,902 SystemKeyspaceMigrator41.java:234 - Migrated 0 rows from legacy compaction_history to compaction_history
INFO  [main] 2025-08-01T20:09:16,022 CommitLog.java:201 - No commitlog files found; skipping replay
INFO  [main] 2025-08-01T20:09:16,022 PaxosStateTracker.java:166 - Beginning uncommitted paxos data rebuild. Set -Dcassandra.skip_paxos_state_rebuild=true and restart to skip
INFO  [main] 2025-08-01T20:09:16,023 PaxosUncommittedTracker.java:232 - truncating paxos uncommitted info
INFO  [main] 2025-08-01T20:09:16,026 PaxosUncommittedTracker.java:242 - enabling PaxosUncommittedTracker
INFO  [main] 2025-08-01T20:09:16,027 PaxosStateTracker.java:306 - Uncommitted paxos data rebuild completed
INFO  [main] 2025-08-01T20:09:16,031 ColumnFamilyStore.java:2476 - Truncating system.size_estimates
INFO  [main] 2025-08-01T20:09:16,034 ColumnFamilyStore.java:2510 - Truncating system.size_estimates with truncatedAt=1754078956033
INFO  [main] 2025-08-01T20:09:16,035 ColumnFamilyStore.java:1052 - Enqueuing flush of system.local, Reason: INTERNALLY_FORCED, Usage: 629B (0%) on-heap, 0B (0%) off-heap
INFO  [PerDiskMemtableFlushWriter_0:2] 2025-08-01T20:09:16,040 Flushing.java:153 - Writing Memtable-local@1008419558(97B serialized bytes, 1 ops, 629B (0%) on-heap, 0B (0%) off-heap), flushed range = [min(-9223372036854775808), max(9223372036854775807))
INFO  [PerDiskMemtableFlushWriter_0:2] 2025-08-01T20:09:16,041 Flushing.java:179 - Completed flushing /testbed/data/data/system/local-7ad54392bcdd35a684174e047860b377/oa-2-big-Data.db (53B) for commitlog position CommitLogPosition(segmentId=1754078953681, position=24786)
INFO  [MemtableFlushWriter:1] 2025-08-01T20:09:16,051 LogTransaction.java:254 - Unfinished transaction log, deleting /testbed/data/data/system/local-7ad54392bcdd35a684174e047860b377/oa_txn_flush_66547c40-6f13-11f0-b119-c19ab67cbe2a.log 
INFO  [main] 2025-08-01T20:09:16,053 ColumnFamilyStore.java:2534 - Truncate of system.size_estimates is complete
INFO  [main] 2025-08-01T20:09:16,053 ColumnFamilyStore.java:2476 - Truncating system.table_estimates
INFO  [main] 2025-08-01T20:09:16,054 ColumnFamilyStore.java:2510 - Truncating system.table_estimates with truncatedAt=1754078956054
INFO  [main] 2025-08-01T20:09:16,055 ColumnFamilyStore.java:1052 - Enqueuing flush of system.local, Reason: INTERNALLY_FORCED, Usage: 629B (0%) on-heap, 0B (0%) off-heap
INFO  [PerDiskMemtableFlushWriter_0:1] 2025-08-01T20:09:16,059 Flushing.java:153 - Writing Memtable-local@1978501689(97B serialized bytes, 1 ops, 629B (0%) on-heap, 0B (0%) off-heap), flushed range = [min(-9223372036854775808), max(9223372036854775807))
INFO  [PerDiskMemtableFlushWriter_0:1] 2025-08-01T20:09:16,060 Flushing.java:179 - Completed flushing /testbed/data/data/system/local-7ad54392bcdd35a684174e047860b377/oa-3-big-Data.db (53B) for commitlog position CommitLogPosition(segmentId=1754078953681, position=24891)
INFO  [MemtableFlushWriter:2] 2025-08-01T20:09:16,068 LogTransaction.java:254 - Unfinished transaction log, deleting /testbed/data/data/system/local-7ad54392bcdd35a684174e047860b377/oa_txn_flush_66578980-6f13-11f0-b119-c19ab67cbe2a.log 
INFO  [main] 2025-08-01T20:09:16,070 ColumnFamilyStore.java:2534 - Truncate of system.table_estimates is complete
INFO  [main] 2025-08-01T20:09:16,077 LocalSessions.java:390 - LocalSessions start completed in 5 ms, sessions loaded from DB: 0
INFO  [main] 2025-08-01T20:09:16,079 QueryProcessor.java:227 - Preloaded 0 prepared statements in 1 ms
INFO  [main] 2025-08-01T20:09:16,079 StorageService.java:719 - Cassandra version: 5.1-SNAPSHOT
INFO  [main] 2025-08-01T20:09:16,079 StorageService.java:720 - Build Date: 2025-08-01T20:03:04.792Z
INFO  [main] 2025-08-01T20:09:16,079 StorageService.java:721 - Git SHA: 58ee8479683f7fa508e78712e5735dfcf0cf09a7
INFO  [main] 2025-08-01T20:09:16,079 StorageService.java:722 - CQL version: 3.4.8
INFO  [main] 2025-08-01T20:09:16,080 StorageService.java:723 - Native protocol supported versions: 3/v3, 4/v4, 5/v5, 6/v6-beta (default: 5/v5)
INFO  [main] 2025-08-01T20:09:16,082 IndexSummaryManager.java:105 - Initializing index summary manager with a memory pool size of 400 MB and a resize interval of 60 minutes
INFO  [main] 2025-08-01T20:09:16,187 ColumnFamilyStore.java:1052 - Enqueuing flush of system.local, Reason: INTERNALLY_FORCED, Usage: 509B (0%) on-heap, 0B (0%) off-heap
INFO  [PerDiskMemtableFlushWriter_0:2] 2025-08-01T20:09:16,191 Flushing.java:153 - Writing Memtable-local@1509188985(53B serialized bytes, 1 ops, 509B (0%) on-heap, 0B (0%) off-heap), flushed range = [min(-9223372036854775808), max(9223372036854775807))
INFO  [PerDiskMemtableFlushWriter_0:2] 2025-08-01T20:09:16,192 Flushing.java:179 - Completed flushing /testbed/data/data/system/local-7ad54392bcdd35a684174e047860b377/oa-4-big-Data.db (18B) for commitlog position CommitLogPosition(segmentId=1754078953681, position=70097)
INFO  [MemtableFlushWriter:1] 2025-08-01T20:09:16,200 LogTransaction.java:254 - Unfinished transaction log, deleting /testbed/data/data/system/local-7ad54392bcdd35a684174e047860b377/oa_txn_flush_666badc0-6f13-11f0-b119-c19ab67cbe2a.log 
INFO  [main] 2025-08-01T20:09:16,259 Gossiper.java:1954 - Waiting for gossip to settle...
INFO  [main] 2025-08-01T20:09:24,263 Gossiper.java:1985 - No gossip backlog; proceeding
INFO  [main] 2025-08-01T20:09:24,280 AbstractLocalProcessor.java:105 - Committed Register{addresses=NodeAddresses{broadcastAddress=localhost/127.0.0.1:7000, localAddress=localhost/127.0.0.1:7000, nativeAddress=localhost/127.0.0.1:9042}, location=datacenter1/rack1, version=NodeVersion{cassandraVersion=5.1.0-SNAPSHOT, serializationVersion=7}}. New epoch is Epoch{epoch=3}
INFO  [GlobalLogFollower] 2025-08-01T20:09:24,283 ColumnFamilyStore.java:1052 - Enqueuing flush of system.local_metadata_log, Reason: INTERNALLY_FORCED, Usage: 787B (0%) on-heap, 0B (0%) off-heap
INFO  [PerDiskMemtableFlushWriter_0:1] 2025-08-01T20:09:24,291 Flushing.java:153 - Writing Memtable-local_metadata_log@1904035150(176B serialized bytes, 1 ops, 787B (0%) on-heap, 0B (0%) off-heap), flushed range = [null, null)
INFO  [PerDiskMemtableFlushWriter_0:1] 2025-08-01T20:09:24,291 Flushing.java:179 - Completed flushing /testbed/data/data/system/local_metadata_log-e4ddd16ebea631f48361e88f07af8d4e/oa-3-big-Data.db (107B) for commitlog position CommitLogPosition(segmentId=1754078953681, position=71282)
INFO  [MemtableFlushWriter:2] 2025-08-01T20:09:24,300 LogTransaction.java:254 - Unfinished transaction log, deleting /testbed/data/data/system/local_metadata_log-e4ddd16ebea631f48361e88f07af8d4e/oa_txn_flush_6b3f07c0-6f13-11f0-b119-c19ab67cbe2a.log 
INFO  [GlobalLogFollower] 2025-08-01T20:09:24,302 LocalLog.java:541 - Enacted EXECUTED {Register{addresses=NodeAddresses{broadcastAddress=localhost/127.0.0.1:7000, localAddress=localhost/127.0.0.1:7000, nativeAddress=localhost/127.0.0.1:9042}, location=datacenter1/rack1, version=NodeVersion{cassandraVersion=5.1.0-SNAPSHOT, serializationVersion=7}}}. New tail is Epoch{epoch=3}
INFO  [main] 2025-08-01T20:09:24,305 Register.java:127 - Registered with endpoint localhost/127.0.0.1:7000, node id: NodeId{id=1}
INFO  [OptionalTasks:1] 2025-08-01T20:09:24,306 ColumnFamilyStore.java:1052 - Enqueuing flush of system.local, Reason: INTERNALLY_FORCED, Usage: 509B (0%) on-heap, 0B (0%) off-heap
INFO  [PerDiskMemtableFlushWriter_0:2] 2025-08-01T20:09:24,309 Flushing.java:153 - Writing Memtable-local@162403754(89B serialized bytes, 2 ops, 629B (0%) on-heap, 0B (0%) off-heap), flushed range = [min(-9223372036854775808), max(9223372036854775807))
INFO  [PerDiskMemtableFlushWriter_0:2] 2025-08-01T20:09:24,310 Flushing.java:179 - Completed flushing /testbed/data/data/system/local-7ad54392bcdd35a684174e047860b377/oa-5-big-Data.db (37B) for commitlog position CommitLogPosition(segmentId=1754078953681, position=71442)
INFO  [MemtableFlushWriter:1] 2025-08-01T20:09:24,319 LogTransaction.java:254 - Unfinished transaction log, deleting /testbed/data/data/system/local-7ad54392bcdd35a684174e047860b377/oa_txn_flush_6b426320-6f13-11f0-b119-c19ab67cbe2a.log 
INFO  [main] 2025-08-01T20:09:24,323 Register.java:141 - New node ID obtained 6d194555-f6eb-41d0-c000-000000000001, (Note: This should happen exactly once per node)
INFO  [main] 2025-08-01T20:09:24,323 RegistrationStatus.java:63 - This node is registered, moving state to REGISTERED and interrupting any previously established peer connections
INFO  [OptionalTasks:1] 2025-08-01T20:09:24,327 ColumnFamilyStore.java:1052 - Enqueuing flush of system.local, Reason: INTERNALLY_FORCED, Usage: 625B (0%) on-heap, 0B (0%) off-heap
INFO  [PerDiskMemtableFlushWriter_0:1] 2025-08-01T20:09:24,336 Flushing.java:153 - Writing Memtable-local@1027144804(85B serialized bytes, 1 ops, 625B (0%) on-heap, 0B (0%) off-heap), flushed range = [min(-9223372036854775808), max(9223372036854775807))
INFO  [PerDiskMemtableFlushWriter_0:1] 2025-08-01T20:09:24,337 Flushing.java:179 - Completed flushing /testbed/data/data/system/local-7ad54392bcdd35a684174e047860b377/oa-6-big-Data.db (33B) for commitlog position CommitLogPosition(segmentId=1754078953681, position=71531)
INFO  [MemtableFlushWriter:2] 2025-08-01T20:09:24,351 LogTransaction.java:254 - Unfinished transaction log, deleting /testbed/data/data/system/local-7ad54392bcdd35a684174e047860b377/oa_txn_flush_6b460ca0-6f13-11f0-b119-c19ab67cbe2a.log 
INFO  [OptionalTasks:1] 2025-08-01T20:09:24,353 LegacyStateListener.java:127 - Peer with address localhost/127.0.0.1:7000 has registered, interrupting any previously established connections
INFO  [main] 2025-08-01T20:09:24,385 TokenAllocatorFactory.java:44 - Using ReplicationAwareTokenAllocator.
INFO  [main] 2025-08-01T20:09:24,471 TokenAllocation.java:128 - Selected tokens [-2766960505398865240, 8441447003329217676, 3631097044087318242, -5658004886782940078, 1033928295874160869, 6505773245145843659, -7493528224137717888, -556076256639737768, -3846333312846466897, 5409731344910905370, 2652951591571204166, -8565028840477750701, -1523440499718273266, 7597070355838055918, -6475676819395290872, -4582249005557360538]
INFO  [main] 2025-08-01T20:09:24,523 AbstractLocalProcessor.java:105 - Committed UnsafeJoin{nodeId=NodeId{id=1}, tokens=[8441447003329217676, -5658004886782940078, -7493528224137717888, -6475676819395290872, -3846333312846466897, 5409731344910905370, -1523440499718273266, 6505773245145843659, 3631097044087318242, -8565028840477750701, -2766960505398865240, -556076256639737768, 2652951591571204166, 7597070355838055918, -4582249005557360538, 1033928295874160869], joinTokenRing=true, streamData=false}. New epoch is Epoch{epoch=4}
INFO  [GlobalLogFollower] 2025-08-01T20:09:24,530 ColumnFamilyStore.java:1052 - Enqueuing flush of system.local_metadata_log, Reason: INTERNALLY_FORCED, Usage: 912B (0%) on-heap, 0B (0%) off-heap
INFO  [PerDiskMemtableFlushWriter_0:2] 2025-08-01T20:09:24,537 Flushing.java:153 - Writing Memtable-local_metadata_log@1106626745(301B serialized bytes, 1 ops, 912B (0%) on-heap, 0B (0%) off-heap), flushed range = [null, null)
INFO  [PerDiskMemtableFlushWriter_0:2] 2025-08-01T20:09:24,538 Flushing.java:179 - Completed flushing /testbed/data/data/system/local_metadata_log-e4ddd16ebea631f48361e88f07af8d4e/oa-4-big-Data.db (234B) for commitlog position CommitLogPosition(segmentId=1754078953681, position=73220)
INFO  [MemtableFlushWriter:1] 2025-08-01T20:09:24,549 LogTransaction.java:254 - Unfinished transaction log, deleting /testbed/data/data/system/local_metadata_log-e4ddd16ebea631f48361e88f07af8d4e/oa_txn_flush_6b64b830-6f13-11f0-b119-c19ab67cbe2a.log 
INFO  [GlobalLogFollower] 2025-08-01T20:09:24,551 LocalLog.java:541 - Enacted EXECUTED {UnsafeJoin{nodeId=NodeId{id=1}, tokens=[8441447003329217676, -5658004886782940078, -7493528224137717888, -6475676819395290872, -3846333312846466897, 5409731344910905370, -1523440499718273266, 6505773245145843659, 3631097044087318242, -8565028840477750701, -2766960505398865240, -556076256639737768, 2652951591571204166, 7597070355838055918, -4582249005557360538, 1033928295874160869], joinTokenRing=true, streamData=false}}. New tail is Epoch{epoch=4}
INFO  [OptionalTasks:1] 2025-08-01T20:09:24,560 ColumnFamilyStore.java:1052 - Enqueuing flush of system.local, Reason: INTERNALLY_FORCED, Usage: 3.446KiB (0%) on-heap, 0B (0%) off-heap
INFO  [main] 2025-08-01T20:09:24,561 ColumnFamilyStore.java:1052 - Enqueuing flush of system.local, Reason: INTERNALLY_FORCED, Usage: 514B (0%) on-heap, 0B (0%) off-heap
INFO  [PerDiskMemtableFlushWriter_0:1] 2025-08-01T20:09:24,566 Flushing.java:153 - Writing Memtable-local@2037914333(673B serialized bytes, 1 ops, 3.446KiB (0%) on-heap, 0B (0%) off-heap), flushed range = [min(-9223372036854775808), max(9223372036854775807))
INFO  [PerDiskMemtableFlushWriter_0:2] 2025-08-01T20:09:24,566 Flushing.java:153 - Writing Memtable-local@656387603(58B serialized bytes, 1 ops, 514B (0%) on-heap, 0B (0%) off-heap), flushed range = [min(-9223372036854775808), max(9223372036854775807))
INFO  [PerDiskMemtableFlushWriter_0:2] 2025-08-01T20:09:24,566 Flushing.java:179 - Completed flushing /testbed/data/data/system/local-7ad54392bcdd35a684174e047860b377/oa-8-big-Data.db (24B) for commitlog position CommitLogPosition(segmentId=1754078953681, position=73706)
INFO  [PerDiskMemtableFlushWriter_0:1] 2025-08-01T20:09:24,566 Flushing.java:179 - Completed flushing /testbed/data/data/system/local-7ad54392bcdd35a684174e047860b377/oa-7-big-Data.db (361B) for commitlog position CommitLogPosition(segmentId=1754078953681, position=73630)
INFO  [MemtableFlushWriter:2] 2025-08-01T20:09:24,577 LogTransaction.java:254 - Unfinished transaction log, deleting /testbed/data/data/system/local-7ad54392bcdd35a684174e047860b377/oa_txn_flush_6b694c10-6f13-11f0-b119-c19ab67cbe2a.log 
INFO  [MemtableFlushWriter:1] 2025-08-01T20:09:24,578 LogTransaction.java:254 - Unfinished transaction log, deleting /testbed/data/data/system/local-7ad54392bcdd35a684174e047860b377/oa_txn_flush_6b697320-6f13-11f0-b119-c19ab67cbe2a.log 
INFO  [main] 2025-08-01T20:09:24,582 Startup.java:460 - NORMAL
WARN  [OptionalTasks:1] 2025-08-01T20:09:24,585 SystemKeyspace.java:1261 - Using stored Gossip Generation 1754078965 as it is greater than current system time 1754078964.  See CASSANDRA-3654 if you experience problems
INFO  [OptionalTasks:1] 2025-08-01T20:09:24,586 ColumnFamilyStore.java:1052 - Enqueuing flush of system.local, Reason: INTERNALLY_FORCED, Usage: 509B (0%) on-heap, 0B (0%) off-heap
INFO  [main] 2025-08-01T20:09:24,590 CassandraRoleManager.java:794 - Invalid role disconnection is disabled
INFO  [PerDiskMemtableFlushWriter_0:1] 2025-08-01T20:09:24,591 Flushing.java:153 - Writing Memtable-local@1625160585(53B serialized bytes, 1 ops, 509B (0%) on-heap, 0B (0%) off-heap), flushed range = [min(-9223372036854775808), max(9223372036854775807))
INFO  [PerDiskMemtableFlushWriter_0:1] 2025-08-01T20:09:24,592 Flushing.java:179 - Completed flushing /testbed/data/data/system/local-7ad54392bcdd35a684174e047860b377/oa-9-big-Data.db (18B) for commitlog position CommitLogPosition(segmentId=1754078953681, position=73781)
INFO  [MemtableFlushWriter:2] 2025-08-01T20:09:24,617 LogTransaction.java:254 - Unfinished transaction log, deleting /testbed/data/data/system/local-7ad54392bcdd35a684174e047860b377/oa_txn_flush_6b6d1ca0-6f13-11f0-b119-c19ab67cbe2a.log 
INFO  [main] 2025-08-01T20:09:24,631 CassandraDaemon.java:428 - Prewarming of auth caches is disabled
Setup successful

Setup success: {'success': True}
