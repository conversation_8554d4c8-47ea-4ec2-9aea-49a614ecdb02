diff --git a/verify_autossh.sh b/verify_autossh.sh
new file mode 100755
index 0000000..1359c4f
--- /dev/null
+++ b/verify_autossh.sh
@@ -0,0 +1,24 @@
+#!/usr/bin/env bash
+
+# verify_autossh.sh
+# Simple script to verify that autossh supervisor process is running and logs exist.
+
+LOGFILE=/var/log/autossh.log
+SUPERVISOR_CONF=/etc/supervisor/conf.d/autossh.conf
+
+if [ ! -f "$SUPERVISOR_CONF" ]; then
+  echo "Supervisor config not found: $SUPERVISOR_CONF" >&2
+  exit 1
+fi
+
+if ! supervisorctl status autossh | grep -q RUNNING; then
+  echo "autossh not running" >&2
+  exit 1
+fi
+
+if [ ! -f "$LOGFILE" ]; then
+  echo "Log file not found: $LOGFILE" >&2
+  exit 1
+fi
+
+echo "AUTOSSH_SUPERVISOR_OK"
