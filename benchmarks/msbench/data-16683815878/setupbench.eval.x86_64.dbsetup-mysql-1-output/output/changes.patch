diff --git a/setup_env.sh b/setup_env.sh
new file mode 100755
index 0000000..d571ef7
--- /dev/null
+++ b/setup_env.sh
@@ -0,0 +1,30 @@
+#!/usr/bin/env bash
+
+# Non‑interactive installation of MySQL and database import.
+# Designed for Ubuntu 22.04 minimal container.
+
+set -e
+
+export DEBIAN_FRONTEND=noninteractive
+
+# Update package list and install MySQL server and gzip
+apt-get update -y
+apt-get install -y mysql-server gzip
+
+# Ensure MySQL service is running
+service mysql start
+
+# Path to the compressed dump inside the repo
+DUMP="$(dirname "$0")/dump.sql.gz"
+if [[ -f "$DUMP" ]]; then
+  echo "Importing database from $DUMP ..."
+  gunzip < "$DUMP" | mysql -u root
+else
+  echo "Warning: $DUMP not found. Skipping import."
+fi
+
+# Show databases for verification
+mysql -u root -e "SHOW DATABASES;"
+
+# Indicate success
+echo "Setup completed successfully."
\ No newline at end of file
